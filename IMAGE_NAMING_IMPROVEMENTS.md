# Image Naming Improvements - Discord-like UX

## Overview

This update improves the image support in message input by implementing a Discord-like UX flow that allows users to rename image attachments before sending messages.

## Features Implemented

### 1. Auto-naming for Pasted Images
- **Before**: Pasted images got generic names like "image.png"
- **After**: Pasted images get descriptive names like "Screenshot 12-27 at 14-30-25.png"
- **Logic**: 
  - If the file has a meaningful name (not generic), it's preserved
  - For generic/pasted images, generates timestamp-based names
  - Format: `Screenshot MM-DD at HH-MM-SS.extension`

### 2. Editable Image Names
- **Click to Edit**: Users can click on any attachment name to edit it
- **Keyboard Support**: 
  - Enter to save changes
  - Escape to cancel editing
  - Auto-focus and text selection for easy editing
- **Visual Feedback**: 
  - Hover effects show the name is clickable
  - Edit icon appears on hover (when rename is available)
  - Inline editing with save/cancel buttons

### 3. Smart Extension Handling
- **Extension Preservation**: For images, the original extension is preserved if the user doesn't specify one
- **Extension Validation**: Validates common image extensions (png, jpg, jpeg, gif, webp, svg, bmp)
- **Auto-correction**: If user removes extension or uses invalid one, the original is restored

### 4. Enhanced UX Features
- **Name Validation**: Prevents empty names
- **Visual States**: Clear editing vs. display states
- **Accessibility**: Proper ARIA labels and keyboard navigation
- **Responsive**: Works well on different screen sizes

## Components Modified

### 1. `AttachmentPill.tsx`
- Added editable name functionality
- Implemented inline editing with input field
- Added validation and extension handling
- Enhanced visual feedback and hover states

### 2. `MessageInput.tsx`
- Added `generateImageName()` function for better auto-naming
- Added `renameAttachment()` handler
- Updated AttachmentPill usage to include rename functionality

### 3. `NewMessageDialog.tsx`
- Same improvements as MessageInput for consistency
- Unified naming logic across both components

## Usage

### For Users:
1. **Paste an image**: It automatically gets a descriptive name
2. **Click the name**: Enter edit mode to rename the attachment
3. **Edit the name**: Type a new name (extension is preserved automatically)
4. **Save**: Press Enter or click the check button
5. **Cancel**: Press Escape or click the X button

### For Developers:
```tsx
// The AttachmentPill now accepts an optional onRename prop
<AttachmentPill 
  attachment={attachment} 
  onRemove={removeAttachment} 
  onRename={renameAttachment}  // Optional: enables editing
/>

// Rename handler updates the attachment name
const renameAttachment = (attachmentId: string, newName: string) => {
  setPendingAttachments(prev => 
    prev.map(att => 
      att.id === attachmentId ? { ...att, name: newName } : att
    )
  );
};
```

## Benefits

1. **Better Organization**: Descriptive names make it easier to identify attachments
2. **User Control**: Users can customize names to match their workflow
3. **Professional Feel**: Similar to Discord's polished UX
4. **Accessibility**: Keyboard navigation and screen reader support
5. **Consistency**: Same behavior across all message input components

## Future Enhancements

- **Bulk Rename**: Select multiple attachments and rename them with a pattern
- **Name Templates**: User-defined templates for auto-naming
- **Duplicate Detection**: Warn when names conflict with existing files
- **Advanced Validation**: Custom validation rules for different file types
