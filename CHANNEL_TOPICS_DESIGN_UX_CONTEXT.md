# Channel Topics - Design & UX Context Note

This document outlines the design principles, user experience (UX) considerations, and high-level integration points for the Channel Topics feature. It complements the technical details found in the `SUPABASE_HIGH_LEVEL_INTEGRATION_PLAN.md`.

## I. Guiding Principles for Channel Topics

1.  **Optionality:**
    *   The core application (messaging, channels, etc.) must function seamlessly even if the Channel Topics feature is not actively used or if no topics are defined within a channel.
    *   UI elements related to topics should gracefully adapt (e.g., hide or provide clear "empty states") when topics are not present.
    *   The underlying data model (`messages.topic_id`) will be nullable, reflecting this optional association.

2.  **Manual Management as Baseline:**
    *   The initial implementation will prioritize robust and intuitive manual management of topics by users.
    *   Users must have clear control over creating, viewing, editing, deleting, archiving, and unarchiving topics.
    *   Associating messages with topics will primarily be a user-driven action.

3.  **LLM as Augmentation, Not Replacement:**
    *   Future LLM-driven features (e.g., automatic summarization, topic suggestion, message tagging) will build upon and enhance the manual foundation.
    *   LLM capabilities should assist users, not remove their agency over topic organization.

4.  **Archiving for Decluttering:**
    *   Archiving provides a non-destructive way to manage topic lifecycle, allowing users to hide older or less relevant topics without permanent data loss.
    *   Archiving a topic will also archive its associated messages to maintain context. Unarchiving a topic will unarchive messages that were archived along with it.

## II. Core UX Flows for Manual Topic Management

### A. Creating Topics

1.  **Explicit Creation (via `ChannelTopics.tsx` tab):**
    *   Users can navigate to a dedicated "Topics" tab within a channel.
    *   A "New Topic" button will open a dialog prompting for:
        *   **Title (Required):** The main identifier for the topic.
        *   **Summary (Optional):** A brief description of the topic's purpose or content.
    *   Upon creation, the new topic appears in the list, and the user might be taken to its focused view.

2.  **Placeholder Creation (via `NewMessageDialog.tsx`):**
    *   When composing a new message, if a channel is selected, the user can choose to associate the message with a "New Topic".
    *   If "New Topic" is selected:
        *   Upon sending the message, a new `channel_topics` record is created automatically.
        *   **Title Generation:** The system will generate a placeholder title (e.g., "New Discussion from [User Name] - [Date/Time]" or "Topic started: [first ~50 chars of message]").
        *   **Summary:** Initially blank or also derived from the first message.
        *   The sent message is automatically associated with this new topic's ID.
        *   Users can later edit the placeholder title and summary via the `ChannelTopics.tsx` tab.
    *   This flow allows for organic topic creation without interrupting the message composition process.

### B. Reading / Viewing Topics

1.  **Topic List (in `ChannelTopics.tsx`):**
    *   Displays all non-archived topics for the current channel by default.
    *   Each topic item should show: Title, Summary (truncated if long), number of messages, creator (optional), creation/last activity date.
    *   Option to toggle visibility of archived topics. Archived topics should be visually distinct (e.g., greyed out, an "Archived" badge).
    *   Topics might be sortable (e.g., by creation date, last activity, title).

2.  **Focused Topic View (in `ChannelTopics.tsx`):**
    *   Clicking a topic in the list navigates to a view showing:
        *   The full topic title and summary at the top.
        *   A `MessageList` filtered to display only messages associated with this `topic_id`.
        *   A `MessageInput` pre-configured to send new messages to this `topic_id`.
    *   Clear indication if the currently viewed topic is archived.

3.  **Topic Selection (in `NewMessageDialog.tsx`):**
    *   When a channel is selected, a dropdown or similar selector allows users to choose an existing, non-archived topic to associate their new message with.

4.  **(Future) Message-Level Topic Indication:**
    *   In the main channel message list (outside the focused topic view), consider a subtle visual indicator (e.g., a small tag, icon, or prefix) on messages that belong to a topic. Clicking this could navigate to the focused topic view.

### C. Updating Topics

*   **Via `ChannelTopics.tsx`:**
    *   Each topic item in the list (or in the focused topic view header) should have an "Edit" option (e.g., in a "..." context menu).
    *   This opens a dialog pre-filled with the current topic title and summary, allowing modification.
    *   Permissions: Topic creator or workspace/channel admin.

### D. Deleting Topics

*   **Via `ChannelTopics.tsx`:**
    *   An "Delete" option in the topic's context menu.
    *   A confirmation dialog: "Are you sure you want to delete the topic '[Topic Title]'? Messages will no longer be associated with this topic but will remain in the channel."
    *   **Behavior:** The `channel_topics` record is deleted. The `topic_id` field on all associated `messages` is set to `NULL`. Messages themselves are NOT deleted.
    *   Permissions: Topic creator or workspace/channel admin.

### E. Archiving / Unarchiving Topics

*   **Via `ChannelTopics.tsx`:**
    *   "Archive Topic" / "Unarchive Topic" options in the topic's context menu.
    *   Confirmation dialogs for both actions.
    *   **Archiving Behavior:**
        *   The `channel_topics` record is marked `is_archived = TRUE`.
        *   All `messages` currently associated with this `topic_id` are also marked `is_archived = TRUE`.
    *   **Unarchiving Behavior (v1):**
        *   The `channel_topics` record is marked `is_archived = FALSE`.
        *   All `messages` currently associated with this `topic_id` AND currently marked `is_archived = TRUE` are set to `is_archived = FALSE`. (This means if a message was manually archived while the topic was active, and then the topic was archived and unarchived, the message would become unarchived).
    *   Permissions: Topic creator or workspace/channel admin.

## III. Message Association with Topics

*   **From `NewMessageDialog.tsx`:**
    *   If an existing topic is selected, the `topic_id` is passed when sending the message.
    *   If "Create new topic" (placeholder flow) is used, the newly created placeholder topic's ID is used.
*   **From Focused Topic View (`ChannelTopics.tsx`):**
    *   The `MessageInput` in this view automatically associates sent messages with the currently selected `topic_id`.
*   **(Future) Re-assigning/Moving Messages:** Consider UX for moving an existing message or a set of messages from one topic to another, or from no topic to a topic. This is an advanced feature for later.

## IV. LLM Integration Path (High-Level UX Notes)

*   **Summarization:**
    *   When an LLM generates a summary for a topic, this summary should populate/update the `summary` field of the topic.
    *   The UI could indicate if a summary is LLM-generated vs. user-written (e.g., a small icon or note).
    *   Users should be able to edit/override LLM-generated summaries.
*   **Suggested Topics (Future):**
    *   If the LLM suggests topics based on channel conversations, the UI will need a way to present these suggestions (e.g., a "Suggested Topics" panel, inline prompts) and allow users to accept, edit, or dismiss them.
*   **Auto-Tagging (Future):**
    *   If LLMs tag individual messages with topics, the message display will need to reflect these tags, potentially allowing multiple topic tags per message. This would require schema changes (e.g., a junction table `message_topic_associations`).

## V. Open Questions & Future Considerations (UX)

*   **Granular Unarchiving:** If a topic is unarchived, should there be an option to *not* unarchive all its messages, or to selectively unarchive them? (For v1, coupled unarchiving is simpler).
*   **Visibility of Topicless Messages:** How are messages *not* assigned to any topic displayed or accessed when a user is primarily interacting via the "Topics" tab? (They remain in the main channel view).
*   **Topic Discovery:** Beyond the list in `ChannelTopics.tsx`, how else might users discover or navigate to topics? (e.g., search, links in messages).
*   **Permissions for Archiving:** Confirm if only creators/admins can archive/unarchive, or if channel members should have this ability for topics they didn't create. (Current plan: creator/admin).

This note provides a foundation for the UX and design thinking as the Channel Topics feature is developed.