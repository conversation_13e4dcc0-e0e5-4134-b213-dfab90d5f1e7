-- RLS Policies for public.workspaces

-- Policy: Users can create workspaces
-- Roles: authenticated
-- Command: INSERT
-- Using: null
-- With Check: (owner_id = auth.uid())
ALTER TABLE public.workspaces ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can create workspaces" ON public.workspaces;
CREATE POLICY "Users can create workspaces"
ON public.workspaces
FOR INSERT
TO authenticated
WITH CHECK (owner_id = auth.uid());

-- Policy: Users can view workspaces they are members of
-- Roles: authenticated
-- Command: SELECT
-- Using: (EXISTS ( SELECT 1
--    FROM workspace_users wu
--   WHERE ((wu.workspace_id = workspaces.id) AND (wu.user_id = auth.uid()))))
-- With Check: null
DROP POLICY IF EXISTS "Users can view workspaces they are members of" ON public.workspaces;
CREATE POLICY "Users can view workspaces they are members of"
ON public.workspaces
FOR SELECT
TO authenticated
USING (EXISTS ( SELECT 1
   FROM workspace_users wu
  WHERE ((wu.workspace_id = workspaces.id) AND (wu.user_id = auth.uid()))));

-- Policy: Workspace admins can update workspace
-- Roles: authenticated
-- Command: UPDATE
-- Using: is_workspace_admin(id)
-- With Check: null
DROP POLICY IF EXISTS "Workspace admins can update workspace" ON public.workspaces;
CREATE POLICY "Workspace admins can update workspace"
ON public.workspaces
FOR UPDATE
TO authenticated
USING (is_workspace_admin(id));
