-- Database Functions (RPC)

-- Function: public.is_allowed_to_send_to_channel(p_channel_id uuid, p_user_id uuid)
CREATE OR REPLACE FUNCTION public.is_allowed_to_send_to_channel(p_channel_id uuid, p_user_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 STABLE SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  RETURN (
    EXISTS (
      SELECT 1 FROM public.workspace_users wu
      JOIN public.channels ch ON wu.workspace_id = ch.workspace_id
      WHERE ch.id = p_channel_id AND wu.user_id = p_user_id
    )
    OR
    EXISTS (
      SELECT 1 FROM public.channel_members cm
      WHERE cm.channel_id = p_channel_id AND cm.user_id = p_user_id
    )
  );
END;
$function$
;

-- Function: public.handle_new_user()
CREATE OR REPLACE FUNCTION public.handle_new_user()
 RETUR<PERSON> trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
BEGIN
  INSERT INTO public.profiles (id, name, avatar_url, created_at, updated_at)
  VALUES (
    NEW.id,
    COALESCE(NEW.raw_user_meta_data->>'name', NEW.email), -- Changed full_name to name
    NEW.raw_user_meta_data->>'avatar_url',
    NOW(),
    NOW()
  );
  RETURN NEW;
END;
$function$
;

-- Function: public.send_message(p_content text, p_workspace_id uuid, p_channel_id uuid DEFAULT NULL::uuid, p_dm_id uuid DEFAULT NULL::uuid, p_parent_message_id uuid DEFAULT NULL::uuid)
-- This version is kept for potential backward compatibility or if a version without topics/attachments is ever needed.
-- However, the primary version used by the app should be the one below that includes topic_id and attachments_data.
CREATE OR REPLACE FUNCTION public.send_message(p_content text, p_workspace_id uuid, p_channel_id uuid DEFAULT NULL::uuid, p_dm_id uuid DEFAULT NULL::uuid, p_parent_message_id uuid DEFAULT NULL::uuid)
 RETURNS messages
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_sender_id UUID := auth.uid();
  v_new_message public.messages;
BEGIN
  IF (p_channel_id IS NULL AND p_dm_id IS NULL) OR (p_channel_id IS NOT NULL AND p_dm_id IS NOT NULL) THEN
    RAISE EXCEPTION 'Either p_channel_id or p_dm_id must be provided, but not both.';
  END IF;

  IF p_channel_id IS NOT NULL THEN
    IF NOT public.is_allowed_to_send_to_channel(p_channel_id, v_sender_id) THEN
      RAISE EXCEPTION 'User % is not allowed to send messages to channel %', v_sender_id, p_channel_id;
    END IF;
  ELSIF p_dm_id IS NOT NULL THEN
    IF NOT public.is_allowed_to_send_to_dm(p_dm_id, v_sender_id) THEN
      RAISE EXCEPTION 'User % is not allowed to send messages to DM %', v_sender_id, p_dm_id;
    END IF;
  END IF;

  INSERT INTO public.messages (user_id, content, channel_id, dm_id, parent_message_id)
  VALUES (v_sender_id, p_content, p_channel_id, p_dm_id, p_parent_message_id)
  RETURNING * INTO v_new_message;

  RETURN v_new_message;
END;
$function$
;

-- Function: public.send_message(p_content text, p_workspace_id uuid, p_channel_id uuid, p_dm_id uuid, p_parent_message_id uuid, p_topic_id uuid, p_attachments_data jsonb[])
-- Updated version with topic_id and attachments_data
CREATE OR REPLACE FUNCTION public.send_message(
    p_content text,
    p_workspace_id uuid,
    p_channel_id uuid DEFAULT NULL::uuid,
    p_dm_id uuid DEFAULT NULL::uuid,
    p_parent_message_id uuid DEFAULT NULL::uuid,
    p_topic_id uuid DEFAULT NULL::uuid,
    p_attachments_data jsonb[] DEFAULT NULL -- New parameter for attachments
)
 RETURNS messages
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_sender_id UUID := auth.uid();
  v_new_message public.messages;
  v_attachment jsonb;
BEGIN
  IF (p_channel_id IS NULL AND p_dm_id IS NULL) OR (p_channel_id IS NOT NULL AND p_dm_id IS NOT NULL) THEN
    RAISE EXCEPTION 'Either p_channel_id or p_dm_id must be provided, but not both.';
  END IF;

  IF p_channel_id IS NOT NULL THEN
    IF NOT public.is_allowed_to_send_to_channel(p_channel_id, v_sender_id) THEN
      RAISE EXCEPTION 'User % is not allowed to send messages to channel %', v_sender_id, p_channel_id;
    END IF;
  ELSIF p_dm_id IS NOT NULL THEN
    IF NOT public.is_allowed_to_send_to_dm(p_dm_id, v_sender_id) THEN
      RAISE EXCEPTION 'User % is not allowed to send messages to DM %', v_sender_id, p_dm_id;
    END IF;
  END IF;

  INSERT INTO public.messages (user_id, content, channel_id, dm_id, parent_message_id, topic_id)
  VALUES (v_sender_id, p_content, p_channel_id, p_dm_id, p_parent_message_id, p_topic_id)
  RETURNING * INTO v_new_message;

  -- Handle attachments if provided
  IF p_attachments_data IS NOT NULL AND array_length(p_attachments_data, 1) > 0 THEN
    FOREACH v_attachment IN ARRAY p_attachments_data
    LOOP
      INSERT INTO public.files (
        name,
        type,
        size_bytes,
        url, -- Temporarily stores base64 or text content
        uploaded_by_user_id,
        message_id,
        channel_id -- Store channel_id if it's a channel message
      )
      VALUES (
        v_attachment->>'name',
        v_attachment->>'type',
        (v_attachment->>'size')::bigint,
        v_attachment->>'url',
        v_sender_id,
        v_new_message.id,
        p_channel_id -- This will be NULL if p_channel_id is NULL (i.e., for DMs)
      );
    END LOOP;
  END IF;

  RETURN v_new_message;
END;
$function$
;

-- Function: public.is_workspace_member(workspace_id uuid)
CREATE OR REPLACE FUNCTION public.is_workspace_member(p_workspace_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  RETURN EXISTS (
    SELECT 1
    FROM public.workspace_users wu
    WHERE wu.workspace_id = p_workspace_id -- Parameter renamed for clarity and to avoid potential ambiguity
      AND wu.user_id = auth.uid()
  );
END;
$function$
;

-- Function: public.is_workspace_admin(workspace_id uuid)
CREATE OR REPLACE FUNCTION public.is_workspace_admin(workspace_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.workspace_users wu
    WHERE wu.workspace_id = $1 AND wu.user_id = auth.uid() AND wu.role = 'admin'
  );
END;
$function$
;

-- Function: public.is_dm_participant(dm_id uuid)
CREATE OR REPLACE FUNCTION public.is_dm_participant(dm_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
SET search_path TO 'public'
AS $function$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.direct_message_participants dmp
    WHERE dmp.dm_id = $1 AND dmp.user_id = auth.uid()
  );
END;
$function$
;

-- Function: public.is_channel_member(channel_id uuid)
CREATE OR REPLACE FUNCTION public.is_channel_member(channel_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 SECURITY DEFINER
SET search_path TO 'public'
AS $function$
DECLARE
  _is_private BOOLEAN;
  _workspace_id UUID;
BEGIN
  -- Get channel info
  SELECT c.is_private, c.workspace_id INTO _is_private, _workspace_id
  FROM public.channels c WHERE c.id = channel_id;
  
  -- If channel is not private, check if user is workspace member
  IF NOT _is_private THEN
    RETURN public.is_workspace_member(_workspace_id);
  END IF;
  
  -- If channel is private, check if user is channel member
  RETURN EXISTS (
    SELECT 1 FROM public.channel_members cm
    WHERE cm.channel_id = $1 AND cm.user_id = auth.uid()
  );
END;
$function$
;

-- Function: public.is_allowed_to_send_to_dm(p_dm_id uuid, p_user_id uuid)
CREATE OR REPLACE FUNCTION public.is_allowed_to_send_to_dm(p_dm_id uuid, p_user_id uuid)
 RETURNS boolean
 LANGUAGE plpgsql
 STABLE SECURITY DEFINER
 SET search_path TO ''
AS $function$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.direct_message_participants dmp -- schema-qualified
    WHERE dmp.dm_id = p_dm_id AND dmp.user_id = p_user_id
  );
END;
$function$
;

-- Function: public.mark_conversation_as_read(p_conversation_id uuid, p_conversation_type text, p_last_read_message_timestamp timestamptz)
CREATE OR REPLACE FUNCTION public.mark_conversation_as_read(
    p_conversation_id UUID,
    p_conversation_type TEXT,
    p_last_read_message_timestamp TIMESTAMPTZ
)
RETURNS VOID
LANGUAGE plpgsql
VOLATILE
SECURITY DEFINER
SET search_path TO 'public' -- Ensure correct schema context
AS $function$
BEGIN
    -- Optional: Add a CHECK constraint for p_conversation_type if not handled by application
    -- IF p_conversation_type NOT IN ('channel', 'dm') THEN
    --     RAISE EXCEPTION 'Invalid conversation_type: %. Must be ''channel'' or ''dm''.', p_conversation_type;
    -- END IF;

    INSERT INTO public.user_conversation_read_states (
        user_id,
        conversation_id,
        conversation_type,
        last_read_message_timestamp
    )
    VALUES (
        auth.uid(),
        p_conversation_id,
        p_conversation_type,
        p_last_read_message_timestamp
    )
    ON CONFLICT (user_id, conversation_id)
    DO UPDATE SET
        last_read_message_timestamp = EXCLUDED.last_read_message_timestamp,
        conversation_type = EXCLUDED.conversation_type;
END;
$function$
;

-- Function: public.create_direct_message_session(p_target_user_id uuid)
-- Creates or retrieves a 1-on-1 direct message session between the current authenticated user and a target user.
-- Returns JSONB: { id: uuid, created_at: timestamptz, participants: uuid[], is_new: boolean }
CREATE OR REPLACE FUNCTION public.create_direct_message_session(p_target_user_id uuid)
RETURNS jsonb
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    v_current_user_id uuid := auth.uid();
    v_session_id uuid;
    v_session_created_at timestamptz;
    v_participants uuid[];
BEGIN
    -- Ensure target_user_id is not the current user
    IF p_target_user_id = v_current_user_id THEN
        RAISE EXCEPTION 'Cannot create a direct message session with oneself.';
    END IF;

    -- Check for an existing 1-on-1 DM session between the two users
    -- Ensure the session contains exactly these two participants and no others.
    SELECT s.id, s.created_at
    INTO v_session_id, v_session_created_at
    FROM direct_message_sessions s
    WHERE (
        SELECT array_agg(p.user_id ORDER BY p.user_id)
        FROM direct_message_participants p
        WHERE p.dm_id = s.id
    ) = ARRAY[LEAST(v_current_user_id, p_target_user_id), GREATEST(v_current_user_id, p_target_user_id)];

    IF v_session_id IS NOT NULL THEN
        -- Existing session found
        v_participants := ARRAY[v_current_user_id, p_target_user_id];
        RETURN jsonb_build_object(
            'id', v_session_id,
            'created_at', v_session_created_at,
            'participants', v_participants,
            'is_new', false
        );
    ELSE
        -- No existing session, create a new one
        INSERT INTO direct_message_sessions (created_at) -- id is auto-generated
        VALUES (NOW())
        RETURNING id, created_at INTO v_session_id, v_session_created_at;

        -- Add participants
        INSERT INTO direct_message_participants (dm_id, user_id)
        VALUES (v_session_id, v_current_user_id);

        INSERT INTO direct_message_participants (dm_id, user_id)
        VALUES (v_session_id, p_target_user_id);

        v_participants := ARRAY[v_current_user_id, p_target_user_id];
        RETURN jsonb_build_object(
            'id', v_session_id,
            'created_at', v_session_created_at,
            'participants', v_participants,
            'is_new', true
        );
    END IF;
END;
$$;

-- Function: public.toggle_reaction(p_message_id uuid, p_emoji text)
-- Toggles a reaction for the current user on a specific message.
-- Returns: boolean - true if reaction was added, false if removed.
CREATE OR REPLACE FUNCTION public.toggle_reaction(p_message_id uuid, p_emoji text)
 RETURNS boolean
 LANGUAGE plpgsql
 VOLATILE -- Data modifying
 SECURITY DEFINER
 SET search_path TO 'public'
AS $function$
DECLARE
  v_user_id UUID := auth.uid();
  v_reaction_exists BOOLEAN;
  v_reaction_added BOOLEAN;
BEGIN
  -- Check if the reaction already exists
  SELECT EXISTS (
    SELECT 1
    FROM public.reactions r
    WHERE r.message_id = p_message_id
      AND r.user_id = v_user_id
      AND r.emoji = p_emoji
  ) INTO v_reaction_exists;

  IF v_reaction_exists THEN
    -- Reaction exists, so delete it
    DELETE FROM public.reactions r
    WHERE r.message_id = p_message_id
      AND r.user_id = v_user_id
      AND r.emoji = p_emoji;
    v_reaction_added := false;
  ELSE
    -- Reaction does not exist, so insert it
    -- The RLS policy "Users can add reactions to messages they can see"
    -- will ensure the user has permission to react to this message.
    INSERT INTO public.reactions (message_id, user_id, emoji)
    VALUES (p_message_id, v_user_id, p_emoji);
    v_reaction_added := true;
  END IF;

  RETURN v_reaction_added;
END;
$function$
;

-- Channel Topic Management RPC Stubs

-- Function: public.create_channel_topic(p_channel_id uuid, p_title text, p_summary text)
-- Creates a new channel topic. creator_id will be auth.uid().
CREATE OR REPLACE FUNCTION public.create_channel_topic(
    p_channel_id uuid,
    p_title text,
    p_summary text DEFAULT NULL
)
RETURNS public.channel_topics
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    new_topic public.channel_topics;
    v_display_order INTEGER;
BEGIN
    -- Determine the display_order for the new topic.
    -- It's placed at the end of existing topics for that channel.
    SELECT COALESCE(MAX(ct.display_order), -1) + 1
    INTO v_display_order
    FROM public.channel_topics ct
    WHERE ct.channel_id = p_channel_id;

    INSERT INTO public.channel_topics (
        channel_id,
        title,
        summary,
        creator_id,
        display_order
        -- is_archived defaults to false (per schema)
        -- created_at, updated_at default to NOW() (per schema)
    )
    VALUES (
        p_channel_id,
        p_title,
        p_summary,
        auth.uid(),
        v_display_order
    )
    RETURNING * INTO new_topic;

    RETURN new_topic;
END;
$$;

-- Function: public.get_channel_topics(p_channel_id uuid, p_include_archived boolean)
-- Retrieves topics for a given channel.
CREATE OR REPLACE FUNCTION public.get_channel_topics(
    p_channel_id uuid,
    p_include_archived BOOLEAN DEFAULT FALSE
)
RETURNS SETOF public.channel_topics
LANGUAGE plpgsql
STABLE -- Read-only
SECURITY INVOKER -- Changed from DEFINER as RLS on channel_topics will handle auth
SET search_path TO 'public'
AS $$
BEGIN
    RETURN QUERY
    SELECT *
    FROM public.channel_topics ct
    WHERE ct.channel_id = p_channel_id
      AND (p_include_archived OR NOT ct.is_archived) -- If p_include_archived is TRUE, this condition is TRUE. If FALSE, then ct.is_archived must be FALSE.
    ORDER BY ct.display_order ASC;
END;
$$;

-- Function: public.update_channel_topic(p_topic_id uuid, p_title text, p_summary text)
-- Updates an existing channel topic.
CREATE OR REPLACE FUNCTION public.update_channel_topic(
    p_topic_id uuid,
    p_title text DEFAULT NULL,
    p_summary text DEFAULT NULL
)
RETURNS public.channel_topics
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path TO 'public'
AS $$
DECLARE
    updated_topic public.channel_topics;
BEGIN
    UPDATE public.channel_topics
    SET
        title = COALESCE(p_title, title),
        summary = COALESCE(p_summary, summary),
        updated_at = NOW()
    WHERE id = p_topic_id
    RETURNING * INTO updated_topic;

    RETURN updated_topic;
END;
$$;

-- Function: public.delete_channel_topic(p_topic_id uuid)
-- Deletes a channel topic. Unlinks messages from the topic before deleting.
CREATE OR REPLACE FUNCTION public.delete_channel_topic(
    p_topic_id uuid
)
RETURNS public.channel_topics -- Returns the deleted topic record
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path TO 'public'
AS $$
DECLARE
    deleted_topic public.channel_topics;
BEGIN
    -- Unlink messages associated with this topic
    UPDATE public.messages
    SET topic_id = NULL
    WHERE topic_id = p_topic_id;

    -- Delete the topic
    DELETE FROM public.channel_topics
    WHERE id = p_topic_id
    RETURNING * INTO deleted_topic;

    RETURN deleted_topic;
END;
$$;

-- Function: public.archive_channel_topic(p_topic_id uuid)
-- Archives a channel topic and its associated messages. archived_by will be auth.uid().
CREATE OR REPLACE FUNCTION public.archive_channel_topic(
    p_topic_id uuid
)
RETURNS public.channel_topics -- Return the archived topic record
LANGUAGE plpgsql
SECURITY INVOKER
SET search_path TO 'public'
AS $$
DECLARE
    v_archiver_id uuid := auth.uid();
    v_now timestamptz := NOW();
    updated_topic public.channel_topics;
BEGIN
    -- Archive the channel topic
    UPDATE public.channel_topics
    SET
        is_archived = TRUE,
        archived_at = v_now,
        archived_by = v_archiver_id
    WHERE id = p_topic_id
    RETURNING * INTO updated_topic;

    -- Archive all associated messages
    UPDATE public.messages
    SET
        is_archived = TRUE,
        archived_at = v_now,
        archived_by = v_archiver_id
    WHERE topic_id = p_topic_id;

    RETURN updated_topic;
END;
$$;

-- Function: public.unarchive_channel_topic(p_topic_id uuid)
-- Unarchives a channel topic and its messages previously archived with this topic.
CREATE OR REPLACE FUNCTION public.unarchive_channel_topic(
    p_topic_id uuid
)
RETURNS public.channel_topics -- Return the unarchived topic record
LANGUAGE plpgsql
VOLATILE -- Modifies data
SECURITY INVOKER
SET search_path TO 'public'
AS $$
DECLARE
    v_topic_original_archived_by uuid;
    v_topic_original_archived_at timestamptz;
    unarchived_topic public.channel_topics;
BEGIN
    -- Retrieve the original archiver and archive timestamp of the topic
    -- This is done before updating the topic itself.
    SELECT archived_by, archived_at
    INTO v_topic_original_archived_by, v_topic_original_archived_at
    FROM public.channel_topics
    WHERE id = p_topic_id;

    -- If the topic was not archived or these values are null,
    -- we might not want to proceed or handle messages differently.
    -- For now, we assume it was properly archived by `archive_channel_topic`.

    -- Unarchive the channel topic
    UPDATE public.channel_topics
    SET
        is_archived = FALSE,
        archived_at = NULL,
        archived_by = NULL,
        updated_at = NOW()
    WHERE id = p_topic_id
    RETURNING * INTO unarchived_topic;

    -- Unarchive associated messages that were archived with this topic
    -- This is identified by matching the topic_id, and the original archived_by and archived_at
    -- values from when the topic was archived.
    IF v_topic_original_archived_by IS NOT NULL AND v_topic_original_archived_at IS NOT NULL THEN
        UPDATE public.messages
        SET
            is_archived = FALSE,
            archived_at = NULL,
            archived_by = NULL,
            updated_at = NOW() -- Keep updated_at current for messages as well
        WHERE
            topic_id = p_topic_id
            AND messages.archived_by = v_topic_original_archived_by
            AND messages.archived_at = v_topic_original_archived_at;
    ELSE
        -- If the topic didn't have an original archiver/timestamp (e.g., it wasn't archived),
        -- we might still want to unarchive messages linked to this topic if they are archived,
        -- but the original request implies unarchiving messages *archived with the topic*.
        -- For safety and to adhere to the "archived with the topic" logic,
        -- we only unarchive messages if we have the original topic archive details.
        -- Alternatively, if the requirement was to unarchive *all* archived messages for this topic
        -- regardless of how they were archived, the condition above would be simpler.
        -- The current logic is safer and more specific.
        RAISE DEBUG 'Topic % was not previously archived or archive details are missing. Associated messages not unarchived based on original archiver.', p_topic_id;
    END IF;

    RETURN unarchived_topic;
END;
$$;

-- Function: public.create_workspace(p_name text, p_icon_url text)
-- Creates a new workspace and makes the creator an admin.
CREATE OR REPLACE FUNCTION public.create_workspace(
    p_name TEXT,
    p_icon_url TEXT DEFAULT NULL
)
RETURNS public.workspaces -- Returns the newly created workspace row
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path TO 'public'
AS $$
DECLARE
    v_new_workspace public.workspaces;
    v_profile_exists BOOLEAN;
    admin_link_exists BOOLEAN; -- Added for checking workspace_users insert
BEGIN
    -- Check if the calling user has a profile
    SELECT EXISTS (SELECT 1 FROM public.profiles WHERE id = auth.uid()) INTO v_profile_exists;
    IF NOT v_profile_exists THEN
        RAISE EXCEPTION 'User profile does not exist for uid: %. Workspace creation aborted.', auth.uid();
    END IF;

    -- Insert into workspaces table
    INSERT INTO public.workspaces (name, icon_url, owner_id)
    VALUES (p_name, p_icon_url, auth.uid())
    RETURNING * INTO v_new_workspace;

    IF v_new_workspace.id IS NULL THEN
        RAISE EXCEPTION 'Failed to insert into workspaces table, workspace ID is NULL after insert.';
    END IF;

    -- Make the creator an admin of the new workspace
    INSERT INTO public.workspace_users (workspace_id, user_id, role)
    VALUES (v_new_workspace.id, auth.uid(), 'admin');

    -- Verify the admin link was created
    SELECT EXISTS (
      SELECT 1 FROM public.workspace_users wu
      WHERE wu.workspace_id = v_new_workspace.id AND wu.user_id = auth.uid() AND wu.role = 'admin'
    ) INTO admin_link_exists;

    IF NOT admin_link_exists THEN
      RAISE EXCEPTION 'Failed to create admin link in workspace_users for workspace ID % and user ID %', v_new_workspace.id, auth.uid();
    END IF;

    -- The full workspace row is already in v_new_workspace
    RETURN v_new_workspace;
END;
$$;

GRANT EXECUTE ON FUNCTION public.create_workspace(TEXT, TEXT) TO authenticated;
-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.get_channel_topics(uuid, boolean) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_workspace_member(uuid) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_channel_member(uuid) TO authenticated; -- Also grant for this as it's used in RLS

-- Function: public.is_user_direct_member_of_channel(p_channel_id uuid, p_user_id uuid)
-- Checks if a user is a direct member of a channel.
-- Used as a SECURITY DEFINER helper to break RLS recursion.
CREATE OR REPLACE FUNCTION public.is_user_direct_member_of_channel(p_channel_id uuid, p_user_id uuid)
RETURNS boolean
LANGUAGE sql
STABLE
SECURITY DEFINER
SET search_path = public -- Ensure it can find channel_members
AS $function$
  SELECT EXISTS (
    SELECT 1
    FROM channel_members cm
    WHERE cm.channel_id = p_channel_id AND cm.user_id = p_user_id
  );
$function$;

GRANT EXECUTE ON FUNCTION public.is_user_direct_member_of_channel(uuid, uuid) TO authenticated;
