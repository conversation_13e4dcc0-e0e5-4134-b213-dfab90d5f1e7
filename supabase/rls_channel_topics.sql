-- RLS Policies for public.channel_topics

-- Policy: Channel members can create topics
-- Roles: authenticated
-- Command: INSERT
-- Using: null
-- With Check: (is_channel_member(channel_id) AND (creator_id = auth.uid()))
ALTER TABLE public.channel_topics ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Channel members can create topics" ON public.channel_topics;
CREATE POLICY "Channel members can create topics"
ON public.channel_topics
FOR INSERT
TO authenticated
WITH CHECK (is_channel_member(channel_id) AND (creator_id = auth.uid()));

-- Policy: Topic creators or admins can delete topics
-- Roles: authenticated
-- Command: DELETE
-- Using: ((creator_id = auth.uid()) OR (EXISTS ( SELECT 1
--    FROM channels c
--   WHERE ((c.id = channel_topics.channel_id) AND is_workspace_admin(c.workspace_id)))))
-- With Check: null
DROP POLICY IF EXISTS "Topic creators or admins can delete topics" ON public.channel_topics;
CREATE POLICY "Topic creators or admins can delete topics"
ON public.channel_topics
FOR DELETE
TO authenticated
USING ((creator_id = auth.uid()) OR (EXISTS ( SELECT 1
   FROM channels c
  WHERE ((c.id = channel_topics.channel_id) AND is_workspace_admin(c.workspace_id)))));

-- Policy: Topic creators or admins can update topics
-- Roles: authenticated
-- Command: UPDATE
-- Using: ((creator_id = auth.uid()) OR (EXISTS ( SELECT 1
--    FROM channels c
--   WHERE ((c.id = channel_topics.channel_id) AND is_workspace_admin(c.workspace_id)))))
-- With Check: null
DROP POLICY IF EXISTS "Topic creators or admins can update topics" ON public.channel_topics;
CREATE POLICY "Topic creators or admins can update topics"
ON public.channel_topics
FOR UPDATE
TO authenticated
USING ((creator_id = auth.uid()) OR (EXISTS ( SELECT 1
   FROM channels c
  WHERE ((c.id = channel_topics.channel_id) AND is_workspace_admin(c.workspace_id)))));

-- Policy: Users can view channel topics they have access to
-- Roles: authenticated
-- Command: SELECT
-- Using: is_channel_member(channel_id)
-- With Check: null
DROP POLICY IF EXISTS "Users can view channel topics they have access to" ON public.channel_topics;
CREATE POLICY "Users can view channel topics they have access to"
ON public.channel_topics
FOR SELECT
TO authenticated
USING (is_channel_member(channel_id));
