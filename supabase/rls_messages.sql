-- RLS Policies for public.messages

-- Policy: Users can delete their own messages
-- Roles: authenticated
-- Command: DELETE
-- Using: (user_id = auth.uid())
-- With Check: null
ALTER TABLE public.messages ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can delete their own messages" ON public.messages;
CREATE POLICY "Users can delete their own messages"
ON public.messages
FOR DELETE
TO authenticated
USING (user_id = auth.uid());

-- Policy: Users can edit their own messages
-- Roles: authenticated
-- Command: UPDATE
-- Using: (user_id = auth.uid())
-- With Check: null
DROP POLICY IF EXISTS "Users can edit their own messages" ON public.messages;
CREATE POLICY "Users can edit their own messages"
ON public.messages
FOR UPDATE
TO authenticated
USING (user_id = auth.uid());

-- Policy: Users can insert messages as themselves via RPC
-- Roles: authenticated
-- Command: INSERT
-- Using: null
-- With Check: (user_id = auth.uid())
DROP POLICY IF EXISTS "Users can insert messages as themselves via RPC" ON public.messages;
CREATE POLICY "Users can insert messages as themselves via RPC"
ON public.messages
FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid());

-- Policy: Users can send messages to channels and DMs they have access to
-- Roles: authenticated
-- Command: INSERT
-- Using: null
-- With Check: ((user_id = auth.uid()) AND (((channel_id IS NOT NULL) AND (EXISTS ( SELECT 1
--    FROM (channels ch
--      JOIN workspace_users wu ON ((ch.workspace_id = wu.workspace_id)))
--   WHERE ((ch.id = messages.channel_id) AND (wu.user_id = auth.uid())))) AND (EXISTS ( SELECT 1
--    FROM channel_members cm
--   WHERE ((cm.channel_id = messages.channel_id) AND (cm.user_id = auth.uid()))))) OR ((dm_id IS NOT NULL) AND is_dm_participant(dm_id))))
DROP POLICY IF EXISTS "Users can send messages to channels and DMs they have access to" ON public.messages;
CREATE POLICY "Users can send messages to channels and DMs they have access to"
ON public.messages
FOR INSERT
TO authenticated
WITH CHECK (((user_id = auth.uid()) AND (((channel_id IS NOT NULL) AND (EXISTS ( SELECT 1
   FROM (channels ch
     JOIN workspace_users wu ON ((ch.workspace_id = wu.workspace_id)))
  WHERE ((ch.id = messages.channel_id) AND (wu.user_id = auth.uid())))) AND (EXISTS ( SELECT 1
   FROM channel_members cm
  WHERE ((cm.channel_id = messages.channel_id) AND (cm.user_id = auth.uid()))))) OR ((dm_id IS NOT NULL) AND is_dm_participant(dm_id)))));

-- Policy: Users can view messages in accessible channels or DMs
-- Roles: authenticated
-- Command: SELECT
-- Using: (((channel_id IS NOT NULL) AND (EXISTS ( SELECT 1
--    FROM channel_members cm
--   WHERE ((cm.channel_id = messages.channel_id) AND (cm.user_id = auth.uid()))))) OR ((dm_id IS NOT NULL) AND (EXISTS ( SELECT 1
--    FROM direct_message_participants dmp
--   WHERE ((dmp.dm_id = messages.dm_id) AND (dmp.user_id = auth.uid()))))))
-- With Check: null
DROP POLICY IF EXISTS "Users can view messages in accessible channels or DMs" ON public.messages;
CREATE POLICY "Users can view messages in accessible channels or DMs"
ON public.messages
FOR SELECT
TO authenticated
USING ((((channel_id IS NOT NULL) AND (EXISTS ( SELECT 1
   FROM channel_members cm
  WHERE ((cm.channel_id = messages.channel_id) AND (cm.user_id = auth.uid()))))) OR ((dm_id IS NOT NULL) AND (EXISTS ( SELECT 1
   FROM direct_message_participants dmp
  WHERE ((dmp.dm_id = messages.dm_id) AND (dmp.user_id = auth.uid())))))));
