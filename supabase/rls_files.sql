-- RLS Policies for public.files

-- Policy: Users can delete their own files
-- Roles: authenticated
-- Command: DELETE
-- Using: (uploaded_by_user_id = auth.uid())
-- With Check: null
ALTER TABLE public.files ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can delete their own files" ON public.files;
CREATE POLICY "Users can delete their own files"
ON public.files
FOR DELETE
TO authenticated
USING (uploaded_by_user_id = auth.uid());

-- Policy: Users can update their own files
-- Roles: authenticated
-- Command: UPDATE
-- Using: (uploaded_by_user_id = auth.uid())
-- With Check: null
DROP POLICY IF EXISTS "Users can update their own files" ON public.files;
CREATE POLICY "Users can update their own files"
ON public.files
FOR UPDATE
TO authenticated
USING (uploaded_by_user_id = auth.uid());

-- Policy: Users can upload files
-- Roles: authenticated
-- Command: INSERT
-- Using: null
-- With Check: (uploaded_by_user_id = auth.uid())
DROP POLICY IF EXISTS "Users can upload files" ON public.files;
CREATE POLICY "Users can upload files"
ON public.files
FOR INSERT
TO authenticated
WITH CHECK (uploaded_by_user_id = auth.uid());

-- Policy: Users can view files in channels and DMs they have access to
-- Roles: authenticated
-- Command: SELECT
-- Using: (((channel_id IS NULL) AND (message_id IS NULL) AND (uploaded_by_user_id = auth.uid())) OR ((channel_id IS NOT NULL) AND is_channel_member(channel_id)) OR ((message_id IS NOT NULL) AND (EXISTS ( SELECT 1
--    FROM messages m
--   WHERE ((m.id = files.message_id) AND (((m.channel_id IS NOT NULL) AND is_channel_member(m.channel_id)) OR ((m.dm_id IS NOT NULL) AND is_dm_participant(m.dm_id))))))))
-- With Check: null
DROP POLICY IF EXISTS "Users can view files in channels and DMs they have access to" ON public.files;
CREATE POLICY "Users can view files in channels and DMs they have access to"
ON public.files
FOR SELECT TO authenticated
USING (((channel_id IS NULL) AND (message_id IS NULL) AND (uploaded_by_user_id = auth.uid())) OR ((channel_id IS NOT NULL) AND is_channel_member(channel_id)) OR ((message_id IS NOT NULL) AND (EXISTS ( SELECT 1
   FROM messages m
  WHERE ((m.id = files.message_id) AND (((m.channel_id IS NOT NULL) AND is_channel_member(m.channel_id)) OR ((m.dm_id IS NOT NULL) AND is_dm_participant(m.dm_id))))))));
