-- R<PERSON> Policies for public.channels
ALTER TABLE public.channels ENABLE ROW LEVEL SECURITY;

-- Remove existing SELECT policies on channels to avoid conflicts
DROP POLICY IF EXISTS "Users can view channels they are members of" ON public.channels;
DROP POLICY IF EXISTS "Users can view channels they have access to" ON public.channels; -- Old name, if present
DROP POLICY IF EXISTS "Workspace members can see all channels in their workspace" ON public.channels; -- Precautionary drop

-- Create the new policy for channel discoverability
CREATE POLICY "Workspace members can see all channels in their workspace"
ON public.channels
FOR SELECT TO authenticated
USING (
    public.is_workspace_member(channels.workspace_id) -- Any workspace member can see the channel entry
    OR
    public.is_workspace_admin(channels.workspace_id) -- Ad<PERSON> can also see all
);

-- Policy: Workspace admins can delete channels
-- Roles: public
-- Command: DELETE
-- Using: is_workspace_admin(workspace_id)
-- With Check: null
DROP POLICY IF EXISTS "Workspace admins can delete channels" ON public.channels;
CREATE POLICY "Workspace admins can delete channels"
ON public.channels
FOR DELETE
TO public
USING (is_workspace_admin(workspace_id));

-- Policy: Workspace admins can update channels
-- Roles: public
-- Command: UPDATE
-- Using: is_workspace_admin(workspace_id)
-- With Check: null
DROP POLICY IF EXISTS "Workspace admins can update channels" ON public.channels;
CREATE POLICY "Workspace admins can update channels"
ON public.channels
FOR UPDATE
TO public
USING (is_workspace_admin(workspace_id));

-- Drop old policies if they exist by these names
DROP POLICY IF EXISTS "Workspace members can create channels" ON public.channels;
DROP POLICY IF EXISTS "Workspace admins can create channels" ON public.channels;

-- Create new policy allowing any workspace member
CREATE POLICY "Workspace members can create channels"
ON public.channels
FOR INSERT TO authenticated
WITH CHECK (EXISTS (
    SELECT 1 FROM public.workspace_users wu
    WHERE wu.workspace_id = channels.workspace_id AND wu.user_id = auth.uid()
));

-- Policy: Channel members can update channel_note
-- DROP POLICY IF EXISTS "Channel members can update channel_note" ON public.channels;
-- CREATE POLICY "Channel members can update channel_note"
-- ON public.channels
-- FOR UPDATE
-- TO authenticated
-- USING ( -- Condition for which rows this policy applies to for potential update
--     -- Use implicit 'channels' table reference for columns of the current row
--     (NOT channels.is_private AND public.is_workspace_member(channels.workspace_id)) OR
--     (channels.is_private AND EXISTS (
--         SELECT 1 FROM public.channel_members cm
--         WHERE cm.channel_id = channels.id AND cm.user_id = auth.uid()
--     ))
-- )
-- WITH CHECK (
--     -- Make WITH CHECK identical to USING to avoid NEW/OLD parsing issues
--     (NOT channels.is_private AND public.is_workspace_member(channels.workspace_id)) OR
--     (channels.is_private AND EXISTS (
--         SELECT 1 FROM public.channel_members cm
--         WHERE cm.channel_id = channels.id AND cm.user_id = auth.uid()
--     ))
-- );
