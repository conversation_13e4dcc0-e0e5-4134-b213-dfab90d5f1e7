-- RLS Policies for public.profiles

-- Policy: Users can update own profile
-- Roles: authenticated
-- Command: UPDATE
-- Using: (id = auth.uid())
-- With Check: null
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can update own profile" ON public.profiles;
CREATE POLICY "Users can update own profile"
ON public.profiles
FOR UPDATE
TO authenticated
USING (id = auth.uid());

-- Policy: Users can view all profiles
-- Roles: authenticated
-- Command: SELECT
-- Using: (auth.role() = 'authenticated'::text)
-- With Check: null
DROP POLICY IF EXISTS "Users can view all profiles" ON public.profiles;
CREATE POLICY "Users can view all profiles"
ON public.profiles
FOR SELECT
TO authenticated
USING (auth.role() = 'authenticated'::text);
