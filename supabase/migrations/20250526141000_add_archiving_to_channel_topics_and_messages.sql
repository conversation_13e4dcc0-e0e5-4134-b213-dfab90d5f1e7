-- Add archiving columns to channel_topics table
ALTER TABLE public.channel_topics
ADD COLUMN IF NOT EXISTS created_by UUID REFERENCES public.profiles(id),
ADD COLUMN IF NOT EXISTS is_archived BOOLEAN NOT NULL DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS archived_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS archived_by UUID REFERENCES public.profiles(id);

COMMENT ON COLUMN public.channel_topics.is_archived IS 'Flag to indicate if the topic is archived.';
COMMENT ON COLUMN public.channel_topics.archived_at IS 'Timestamp when the topic was archived.';
COMMENT ON COLUMN public.channel_topics.archived_by IS 'User ID of the user who archived the topic.';
COMMENT ON COLUMN public.channel_topics.created_by IS 'User who created the topic.';

-- Add archiving columns to messages table
-- These columns are general purpose for message archiving,
-- and can be used when a topic (and its messages) are archived.
ALTER TABLE public.messages
ADD COLUMN IF NOT EXISTS is_archived BO<PERSON>EAN NOT NULL DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS archived_at TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS archived_by UUID REFERENCES public.profiles(id);

COMMENT ON COLUMN public.messages.is_archived IS 'Flag to indicate if the message is archived (can be due to topic archival or direct message archival).';
COMMENT ON COLUMN public.messages.archived_at IS 'Timestamp when the message was archived.';
COMMENT ON COLUMN public.messages.archived_by IS 'User ID of the user who archived the message.';