-- <PERSON>LS Policies for public.direct_message_participants

-- Policy: Allow inserts if done by the create_direct_message_session RPC (which is SECURITY DEFINER)
-- or if users are adding themselves (though RPC handles both cases now).
-- For SECURITY DEFINER functions, a simple CHECK (true) is often used,
-- relying on the function's internal logic for security.
ALTER TABLE public.direct_message_participants ENABLE ROW LEVEL SECURITY;

-- Drop the overly permissive policy
DROP POLICY IF EXISTS "Allow DM participant inserts" ON public.direct_message_participants;

-- Recreate with WITH CHECK (false) to restrict direct inserts via this policy
CREATE POLICY "Allow DM participant inserts" -- Name can be kept or changed
ON public.direct_message_participants
FOR INSERT TO authenticated
WITH CHECK (false); -- Critical change

-- Ensure the policy allowing users to add themselves (if desired) is correctly defined:
DROP POLICY IF EXISTS "Users can add themselves to DM sessions" ON public.direct_message_participants;
CREATE POLICY "Users can add themselves to DM sessions"
ON public.direct_message_participants
FOR INSERT TO authenticated
WITH CHECK (user_id = auth.uid());

-- Policy: Users can view DM participants
-- Roles: authenticated
-- Command: SELECT
-- Using: is_dm_participant(dm_id)
-- With Check: null
DROP POLICY IF EXISTS "Users can view DM participants" ON public.direct_message_participants;
CREATE POLICY "Users can view DM participants"
ON public.direct_message_participants
FOR SELECT
TO authenticated
USING (is_dm_participant(dm_id));
