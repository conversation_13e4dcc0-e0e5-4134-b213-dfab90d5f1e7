-- RLS Policies for public.user_app_state

-- Policy: Users can create their own app state
-- Roles: authenticated
-- Command: INSERT
-- Using: null
-- With Check: (user_id = auth.uid())
ALTER TABLE public.user_app_state ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can create their own app state" ON public.user_app_state;
CREATE POLICY "Users can create their own app state"
ON public.user_app_state
FOR INSERT
TO authenticated
WITH CHECK (user_id = auth.uid());

-- Policy: Users can update their own app state
-- Roles: authenticated
-- Command: UPDATE
-- Using: (user_id = auth.uid())
-- With Check: null
DROP POLICY IF EXISTS "Users can update their own app state" ON public.user_app_state;
CREATE POLICY "Users can update their own app state"
ON public.user_app_state
FOR UPDATE
TO authenticated
USING (user_id = auth.uid());

-- Policy: Users can view their own app state
-- Roles: authenticated
-- Command: SELECT
-- Using: (user_id = auth.uid())
-- With Check: null
DROP POLICY IF EXISTS "Users can view their own app state" ON public.user_app_state;
CREATE POLICY "Users can view their own app state"
ON public.user_app_state
FOR SELECT
TO authenticated
USING (user_id = auth.uid());
