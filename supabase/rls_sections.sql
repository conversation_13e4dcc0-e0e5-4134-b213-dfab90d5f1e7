-- RLS Policies for public.sections

-- Policy: Users can view sections in their workspaces
-- Roles: authenticated
-- Command: SELECT
-- Using: (EXISTS ( SELECT 1
--    FROM workspace_users wu
--   WHERE ((wu.workspace_id = sections.workspace_id) AND (wu.user_id = auth.uid()))))
-- With Check: null
ALTER TABLE public.sections ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can view sections in their workspaces" ON public.sections;
CREATE POLICY "Users can view sections in their workspaces"
ON public.sections
FOR SELECT
TO authenticated
USING (EXISTS ( SELECT 1
   FROM workspace_users wu
  WHERE ((wu.workspace_id = sections.workspace_id) AND (wu.user_id = auth.uid()))));

-- Policy: Workspace admins can create sections
-- Roles: authenticated
-- Command: INSERT
-- Using: null
-- With Check: is_workspace_admin(workspace_id)
DROP POLICY IF EXISTS "Workspace admins can create sections" ON public.sections;
CREATE POLICY "Workspace admins can create sections"
ON public.sections
FOR INSERT
TO authenticated
WITH CHECK (EXISTS (
    SELECT 1
    FROM public.workspace_users wu
    WHERE wu.workspace_id = sections.workspace_id
      AND wu.user_id = auth.uid()
      AND wu.role = 'admin'
));

-- Policy: Workspace admins can delete sections
-- Roles: authenticated
-- Command: DELETE
-- Using: is_workspace_admin(workspace_id)
-- With Check: null
DROP POLICY IF EXISTS "Workspace admins can delete sections" ON public.sections;
CREATE POLICY "Workspace admins can delete sections"
ON public.sections
FOR DELETE
TO authenticated
USING (EXISTS (
    SELECT 1
    FROM public.workspace_users wu
    WHERE wu.workspace_id = sections.workspace_id
      AND wu.user_id = auth.uid()
      AND wu.role = 'admin'
));

-- Policy: Workspace admins can update sections
-- Roles: authenticated
-- Command: UPDATE
-- Using: is_workspace_admin(workspace_id)
-- With Check: null
DROP POLICY IF EXISTS "Workspace admins can update sections" ON public.sections;
CREATE POLICY "Workspace admins can update sections"
ON public.sections
FOR UPDATE
TO authenticated
USING (EXISTS (
    SELECT 1
    FROM public.workspace_users wu
    WHERE wu.workspace_id = sections.workspace_id
      AND wu.user_id = auth.uid()
      AND wu.role = 'admin'
));
