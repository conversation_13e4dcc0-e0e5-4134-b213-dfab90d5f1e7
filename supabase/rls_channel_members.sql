-- RLS Policies for public.channel_members

-- Policy: Users can view members of channels they can see
-- Roles: authenticated
-- Command: SELECT
-- Using: ( channel_id IN (SELECT id FROM public.channels) )
-- With Check: null
ALTER TABLE public.channel_members ENABLE ROW LEVEL SECURITY;
DROP POLICY IF EXISTS "Users can view channel members" ON public.channel_members; -- Oldest name
DROP POLICY IF EXISTS "Simplified view for channel members" ON public.channel_members; -- Intermediate name
DROP POLICY IF EXISTS "Users can view members of channels they can see" ON public.channel_members; -- Correct name
CREATE POLICY "Users can view members of channels they can see"
ON public.channel_members
FOR SELECT
TO authenticated
USING ( channel_id IN (SELECT id FROM public.channels) );

-- Policy: Workspace admins can delete channel members
-- Roles: authenticated
-- Command: DELETE
-- Using: (EXISTS ( SELECT 1
--    FROM channels c
--   WHERE ((c.id = channel_members.channel_id) AND is_workspace_admin(c.workspace_id))))
-- With Check: null
DROP POLICY IF EXISTS "Workspace admins can delete channel members" ON public.channel_members;
CREATE POLICY "Workspace admins can delete channel members"
ON public.channel_members
FOR DELETE
TO authenticated
USING (EXISTS ( SELECT 1
   FROM channels c
  WHERE ((c.id = channel_members.channel_id) AND is_workspace_admin(c.workspace_id))));

-- Policy: Workspace admins can manage channel members
-- Roles: authenticated
-- Command: INSERT
-- Using: null
-- With Check: (EXISTS ( SELECT 1
--    FROM channels c
--   WHERE ((c.id = channel_members.channel_id) AND is_workspace_admin(c.workspace_id))))
DROP POLICY IF EXISTS "Workspace admins can manage channel members" ON public.channel_members;
CREATE POLICY "Workspace admins can manage channel members"
ON public.channel_members
FOR INSERT
TO authenticated
WITH CHECK (
  -- Condition 1: Workspace admin can add anyone
  (EXISTS (
    SELECT 1
    FROM public.channels c
    WHERE c.id = channel_members.channel_id AND public.is_workspace_admin(c.workspace_id)
  ))
  OR
  -- Condition 2: Workspace member can add themselves
  (
    channel_members.user_id = auth.uid() AND
    EXISTS (
      SELECT 1
      FROM public.workspace_users wu
      JOIN public.channels c ON wu.workspace_id = c.workspace_id
      WHERE c.id = channel_members.channel_id AND wu.user_id = auth.uid()
    )
  )
));
