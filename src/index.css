
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import './styles/keyboard-navigation.css';

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 220 13% 65%;
    --primary-foreground: 220 13% 25%;

    --secondary: 220 14% 98%;
    --secondary-foreground: 220 13% 45%;

    --muted: 220 14% 98%;
    --muted-foreground: 220 9% 55%;

    --accent: 220 14% 98%;
    --accent-foreground: 220 13% 45%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%; /* Reverted to original */
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* RGB values for app-highlight for use in rgba() */
    --app-highlight-rgb: 59, 130, 246;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%; /* Reverted to original */
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* RGB values for app-highlight for use in rgba() */
    --app-highlight-rgb: 59, 130, 246;
  }
}

@layer base {
  /* Removed global border application:
  * {
    @apply border-border;
  }
  */

  body {
    @apply bg-background text-foreground font-sans;
  }
}

.app-sidebar {
  @apply bg-app-sidebar text-app-text;
  /* The @apply above should handle setting background to var(--app-sidebar)
     and text to var(--app-text) based on tailwind.config.ts definitions.
     The explicit line below with a fallback was likely causing issues or overriding. */
}

.channel-hover:hover {
  @apply bg-app-hover;
}

.thread-border {
  @apply border-l border-border pl-4 ml-6 mt-1; /* Changed to 1px and use theme border */
}

.message-input {
  @apply border border-border rounded-md p-2 w-full focus:outline-none focus:ring-2 focus:ring-app-active;
}

/* Slack-like message input styling */
.message-input-editor-wrapper .EasyMDEContainer {
  border: none !important;
  background: transparent !important;
}

.message-input-editor-wrapper .CodeMirror {
  border: none !important;
  background: transparent !important;
  font-family: inherit !important;
  font-size: 14px !important;
  line-height: 1.4 !important;
  padding: 10px 12px !important;
  min-height: 40px !important;
  max-height: none !important;
  color: var(--app-main-text) !important;
  resize: none !important;
}

.message-input-editor-wrapper .CodeMirror-wrap {
  border: none !important;
  background: transparent !important;
}

.message-input-editor-wrapper .CodeMirror-scroll {
  min-height: 40px !important;
  padding: 0 !important;
  overflow-x: hidden !important;
}

.message-input-editor-wrapper .CodeMirror-placeholder {
  color: var(--app-main-text) !important;
  opacity: 0.5 !important;
  font-style: normal !important;
}

.message-input-editor-wrapper .CodeMirror-cursor {
  border-left-color: var(--app-main-text) !important;
}

/* Ensure single line behavior by default */
.message-input-editor-wrapper .CodeMirror-lines {
  padding: 0 !important;
}

.message-input-editor-wrapper .CodeMirror-line {
  padding: 0 !important;
  line-height: 1.4 !important;
}

/* Hide the default SimpleMDE toolbar when not shown */
.message-input-editor-wrapper .editor-toolbar {
  border: none !important;
  background: var(--app-main-bg) !important;
  padding: 8px 12px !important;
  border-bottom: 1px solid var(--app-border) !important;
}

.message-input-editor-wrapper .editor-toolbar.disabled-for-preview {
  background: var(--app-main-bg) !important;
}

.message-input-editor-wrapper .editor-toolbar a {
  color: var(--app-main-text) !important;
  border: none !important;
  background: transparent !important;
}

.message-input-editor-wrapper .editor-toolbar a:hover {
  background: var(--app-hover-bg) !important;
  color: var(--app-main-text) !important;
}

.message-input-editor-wrapper .editor-toolbar a.active {
  background: var(--app-hover-bg) !important;
  color: var(--app-highlight) !important;
}

/* Expand/collapse button positioning */
.message-input-editor-wrapper {
  position: relative;
}

/* Ensure the expand/collapse buttons don't interfere with text input */
.message-input-editor-wrapper .CodeMirror {
  padding-right: 40px !important;
}

.emoji-picker {
  @apply absolute bottom-full mb-2 bg-white shadow-lg rounded-lg p-2 z-10;
}
