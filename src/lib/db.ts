import <PERSON>ie, { Table } from 'dexie';
import {
  User as Profile, // Renaming User to Profile to match DB table and avoid conflict with <PERSON><PERSON>.User
  Workspace,
  Channel,
  DirectMessage as DirectMessageSession, // Renaming DirectMessage to DirectMessageSession
  Message,
  ChannelTopic,
  UserConversationReadState,
  Section, // Added Section
} from './types';

export class AppDexie extends Dexie {
  profiles!: Table<Profile, string>; // string is the type of the primary key (id)
  workspaces!: Table<Workspace, string>;
  sections!: Table<Section, string>; // Added sections table
  channels!: Table<Channel, string>;
  directMessageSessions!: Table<DirectMessageSession, string>;
  messages!: Table<Message, string>;
  channelTopics!: Table<ChannelTopic, string>;
  userConversationReadStates!: Table<UserConversationReadState, [string, string]>; // Compound primary key [user_id, conversation_id]

  private dbReady = false;
  private retryCount = 0;
  private maxRetries = 3;
  private currentUserId: string | null = null;

  constructor(userId?: string) {
    // Create user-specific database name to ensure data isolation
    const dbName = userId ? `ThreadFlowSocialDB_${userId}` : 'ThreadFlowSocialDB_default';
    super(dbName);
    this.currentUserId = userId || null;
    this.version(1).stores({
      profiles: 'id', // Primary key 'id'
      workspaces: 'id',
      channels: 'id, workspace_id, section_id', // Primary key 'id', index on 'workspace_id' and 'section_id'
      directMessageSessions: 'id',
      messages: 'id, channel_id, dm_id, timestamp, [channel_id+timestamp], [dm_id+timestamp]', // Primary key 'id', indexes
      channelTopics: 'id, channel_id',
      userConversationReadStates: '[user_id+conversation_id]', // Compound primary key
    });
    // Add sections table in a new version
    this.version(2).stores({
      profiles: 'id',
      workspaces: 'id',
      sections: 'id, workspace_id, display_order', // Added sections table definition
      channels: 'id, workspace_id, section_id',
      directMessageSessions: 'id',
      messages: 'id, channel_id, dm_id, timestamp, [channel_id+timestamp], [dm_id+timestamp]',
      channelTopics: 'id, channel_id',
      userConversationReadStates: '[user_id+conversation_id]',
    });

    // Version 3: Update messages table schema for correct indexing (camelCase) and add userId index
    this.version(3).stores({
      profiles: 'id',
      workspaces: 'id',
      sections: 'id, workspace_id, display_order',
      channels: 'id, workspace_id, section_id',
      directMessageSessions: 'id',
      messages: 'id, channelId, dmId, timestamp, userId, [channelId+timestamp], [dmId+timestamp]', // Corrected casing and added userId
      channelTopics: 'id, channel_id', // Assuming channel_id is correct here as no error reported
      userConversationReadStates: '[user_id+conversation_id]',
    });

    // Add error handling and recovery mechanisms
    this.on('close', () => {
      this.dbReady = false;
    });

    this.on('ready', () => {
      this.dbReady = true;
      this.retryCount = 0; // Reset retry count on successful open
    });
  }

  // Method to get current user ID
  getCurrentUserId(): string | null {
    return this.currentUserId;
  }

  // Robust database operation wrapper with retry logic
  async safeOperation<T>(operation: () => Promise<T>, operationName: string): Promise<T | null> {
    for (let attempt = 0; attempt <= this.maxRetries; attempt++) {
      try {
        // Ensure database is open before operation
        if (!this.isOpen()) {
          await this.open();
        }

        const result = await operation();
        this.retryCount = 0; // Reset on success
        return result;
      } catch (error: any) {
        console.error(`[DB] Error in ${operationName} (attempt ${attempt + 1}/${this.maxRetries + 1}):`, error.message);

        // Check if it's a database corruption or access error
        if (error.name === 'DatabaseClosedError' ||
            error.name === 'OpenFailedError' ||
            error.message?.includes('backing store') ||
            error.message?.includes('UnknownError')) {

          if (attempt < this.maxRetries) {
            await this.recoverDatabase();
            await new Promise(resolve => setTimeout(resolve, 100 * (attempt + 1))); // Exponential backoff
          } else {
            console.error(`[DB] Failed to recover database after ${this.maxRetries} attempts for ${operationName}`);
            return null;
          }
        } else {
          // For other errors, don't retry
          console.error(`[DB] Non-recoverable error in ${operationName}:`, error);
          return null;
        }
      }
    }
    return null;
  }

  // Database recovery method
  async recoverDatabase(): Promise<void> {
    try {
      // Close the database if it's open
      if (this.isOpen()) {
        this.close();
      }

      // Wait a bit before reopening
      await new Promise(resolve => setTimeout(resolve, 200));

      // Try to reopen
      await this.open();
    } catch (error: any) {
      // If recovery fails completely, we might need to delete and recreate
      if (error.message?.includes('backing store') || error.name === 'OpenFailedError') {
        try {
          await this.delete();
          await new Promise(resolve => setTimeout(resolve, 500));
          await this.open();
        } catch (recreateError: any) {
          console.error('[DB] Failed to recreate database:', recreateError.message);
          throw recreateError;
        }
      } else {
        throw error;
      }
    }
  }

  // Database health check
  async healthCheck(): Promise<boolean> {
    try {
      if (!this.isOpen()) {
        await this.open();
      }

      // Try a simple read operation
      await this.profiles.limit(1).toArray();
      return true;
    } catch (error: any) {
      return false;
    }
  }

  // Force database reset (for severe corruption cases)
  async forceReset(): Promise<void> {
    try {
      // Close if open
      if (this.isOpen()) {
        this.close();
      }

      // Delete the database
      await this.delete();

      // Wait a bit
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Reopen
      await this.open();
    } catch (error: any) {
      console.error('[DB] Force reset failed:', error.message);
      throw error;
    }
  }

  // Utility methods to clear all data (useful for development/testing)
  async clearAllData() {
    return this.safeOperation(async () => {
      await Promise.all([
        this.profiles.clear(),
        this.workspaces.clear(),
        this.sections.clear(), // Added sections to clear
        this.channels.clear(),
        this.directMessageSessions.clear(),
        this.messages.clear(),
        this.channelTopics.clear(),
        this.userConversationReadStates.clear(),
      ]);
    }, 'clearAllData');
  }
}

// Global database instance - will be recreated when user changes
export let db = new AppDexie();

// Function to switch database to a different user
export function switchDatabaseUser(userId: string | null): void {
  // Close current database if open
  if (db.isOpen()) {
    db.close();
  }

  // Create new database instance for the user
  db = new AppDexie(userId);
}

// Database initialization utility
const FORCE_SKIP_INDEXEDDB_INIT = true; // ADDED: Flag to skip IndexedDB initialization

export async function initializeDatabase(userId?: string): Promise<boolean> {
  if (FORCE_SKIP_INDEXEDDB_INIT) {
    console.warn('[DB] IndexedDB initialization explicitly skipped by FORCE_SKIP_INDEXEDDB_INIT flag. localStorage will be used via hybridCache.');
    return false; // Indicate IndexedDB is not available
  }
  try {
    // Switch to user-specific database if userId provided
    if (userId) {
      switchDatabaseUser(userId);
    }

    // First, try a health check
    const isHealthy = await db.healthCheck();

    if (isHealthy) {
      return true;
    }

    // If health check fails, try recovery
    await db.recoverDatabase();

    // Check again
    const isHealthyAfterRecovery = await db.healthCheck();

    if (isHealthyAfterRecovery) {
      return true;
    }

    // If still failing, force reset
    await db.forceReset();

    // Final check
    const isHealthyAfterReset = await db.healthCheck();

    if (isHealthyAfterReset) {
      return true;
    }

    console.error('[DB] Database initialization failed completely - IndexedDB appears to be corrupted at system level');
    return false; // Return false to indicate IndexedDB is not available

  } catch (error: any) {
    console.error('[DB] Database initialization error:', error.message);
    return false;
  }
}

// Utility function to expose database reset to global scope for debugging
if (typeof window !== 'undefined') {
  (window as any).resetThreadFlowDB = async () => {
    try {
      console.log('[DB] Manual database reset initiated...');
      await db.forceReset();
      console.log('[DB] Manual database reset completed. Please refresh the page.');
      return 'Database reset successful. Please refresh the page.';
    } catch (error: any) {
      console.error('[DB] Manual database reset failed:', error.message);
      return `Database reset failed: ${error.message}`;
    }
  };

  (window as any).checkThreadFlowDB = async () => {
    try {
      const isHealthy = await db.healthCheck();
      console.log('[DB] Health check result:', isHealthy);
      return isHealthy ? 'Database is healthy' : 'Database has issues';
    } catch (error: any) {
      console.error('[DB] Health check failed:', error.message);
      return `Health check failed: ${error.message}`;
    }
  };
}

// Example usage (optional, for testing or seeding)
// db.transaction('rw', db.profiles, async () => {
//   if ((await db.profiles.count()) === 0) {
//     console.log("Database is empty. Populating with some data...");
//     await db.profiles.add({ id: 'user1', name: 'Alice', avatar: '' });
//   }
// }).catch(e => {
//   console.error("Failed to populate database", e);
// });
