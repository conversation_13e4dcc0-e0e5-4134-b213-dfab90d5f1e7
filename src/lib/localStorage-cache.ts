import {
  User as Profile,
  Workspace,
  Channel,
  DirectMessage as DirectMessageSession,
  Message,
  ChannelTopic,
  UserConversationReadState,
  Section,
} from './types';

// localStorage-based cache implementation as fallback for IndexedDB
class LocalStorageCache {
  private prefix = 'threadflow_';
  private maxCacheSize = 50 * 1024 * 1024; // 50MB limit for localStorage
  private currentUserId: string | null = null;

  // Set the current user for cache isolation
  setCurrentUser(userId: string | null): void {
    this.currentUserId = userId;
  }

  // Helper methods
  private getKey(table: string, id?: string): string {
    if (!this.currentUserId) {
      console.warn('[Cache] No current user set for cache operation');
      return `${this.prefix}${table}${id ? `_${id}` : ''}`;
    }
    return `${this.prefix}${this.currentUserId}_${table}${id ? `_${id}` : ''}`;
  }

  private setItem(key: string, data: any): boolean {
    try {
      const serialized = JSON.stringify(data);

      // Check if we're approaching localStorage limits
      if (serialized.length > this.maxCacheSize / 10) {
        console.warn('[Cache] Large item being stored, may cause issues:', key);
      }

      localStorage.setItem(key, serialized);
      return true;
    } catch (error: any) {
      console.error('[Cache] Failed to store item:', key, error.message);

      // If quota exceeded, try to clear some space
      if (error.name === 'QuotaExceededError') {
        this.clearOldestItems();
        try {
          localStorage.setItem(key, JSON.stringify(data));
          return true;
        } catch (retryError: any) {
          console.error('[Cache] Failed to store item after cleanup:', key, retryError.message);
        }
      }
      return false;
    }
  }

  private getItem<T>(key: string): T | null {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error: any) {
      console.error('[Cache] Failed to retrieve item:', key, error.message);
      return null;
    }
  }

  private removeItem(key: string): void {
    try {
      localStorage.removeItem(key);
    } catch (error: any) {
      console.error('[Cache] Failed to remove item:', key, error.message);
    }
  }

  private clearOldestItems(): void {
    try {
      // Only clear items for the current user to avoid affecting other users' cache
      const userPrefix = this.currentUserId ? `${this.prefix}${this.currentUserId}_` : this.prefix;
      const keys = Object.keys(localStorage).filter(key => key.startsWith(userPrefix));
      // Remove oldest 25% of items (simple strategy)
      const toRemove = keys.slice(0, Math.floor(keys.length * 0.25));
      toRemove.forEach(key => localStorage.removeItem(key));
      // Cleared old cache items to free space
    } catch (error: any) {
      console.error('[Cache] Failed to clear old items:', error.message);
    }
  }

  // Profile operations
  async getProfile(id: string): Promise<Profile | null> {
    return this.getItem<Profile>(this.getKey('profiles', id));
  }

  async putProfile(profile: Profile): Promise<boolean> {
    return this.setItem(this.getKey('profiles', profile.id), profile);
  }

  // Messages operations
  async getMessagesForChannel(channelId: string): Promise<Message[]> {
    return this.getItem<Message[]>(this.getKey('messages_channel', channelId)) || [];
  }

  async putMessagesForChannel(channelId: string, messages: Message[]): Promise<boolean> {
    return this.setItem(this.getKey('messages_channel', channelId), messages);
  }

  async getMessagesForDM(dmId: string): Promise<Message[]> {
    return this.getItem<Message[]>(this.getKey('messages_dm', dmId)) || [];
  }

  async putMessagesForDM(dmId: string, messages: Message[]): Promise<boolean> {
    return this.setItem(this.getKey('messages_dm', dmId), messages);
  }

  // User conversation read states
  async getUserConversationReadStates(userId: string): Promise<UserConversationReadState[]> {
    return this.getItem<UserConversationReadState[]>(this.getKey('read_states', userId)) || [];
  }

  async putUserConversationReadStates(userId: string, states: UserConversationReadState[]): Promise<boolean> {
    return this.setItem(this.getKey('read_states', userId), states);
  }

  // Workspace data
  async getWorkspaceData(userId: string): Promise<any> {
    return this.getItem(this.getKey('workspace', userId));
  }

  async putWorkspaceData(userId: string, data: any): Promise<boolean> {
    return this.setItem(this.getKey('workspace', userId), data);
  }

  // Clear cache for current user only
  async clearAll(): Promise<void> {
    try {
      const userPrefix = this.currentUserId ? `${this.prefix}${this.currentUserId}_` : this.prefix;
      const keys = Object.keys(localStorage).filter(key => key.startsWith(userPrefix));
      keys.forEach(key => localStorage.removeItem(key));
      // Cleared user cache
    } catch (error: any) {
      console.error('[Cache] Failed to clear cache:', error.message);
    }
  }

  // Clear cache for all users (admin function)
  async clearAllUsers(): Promise<void> {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix));
      keys.forEach(key => localStorage.removeItem(key));
      // Cleared all users cache
    } catch (error: any) {
      console.error('[Cache] Failed to clear all users cache:', error.message);
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const testKey = this.getKey('health_check');
      const testData = { timestamp: Date.now() };

      this.setItem(testKey, testData);
      const retrieved = this.getItem(testKey);
      this.removeItem(testKey);

      return retrieved !== null && retrieved.timestamp === testData.timestamp;
    } catch (error: any) {
      console.error('[Cache] Health check failed:', error.message);
      return false;
    }
  }

  // Get cache size info for current user
  getCacheInfo(): { itemCount: number; estimatedSize: string; userId: string | null } {
    try {
      const userPrefix = this.currentUserId ? `${this.prefix}${this.currentUserId}_` : this.prefix;
      const keys = Object.keys(localStorage).filter(key => key.startsWith(userPrefix));
      let totalSize = 0;

      keys.forEach(key => {
        const item = localStorage.getItem(key);
        if (item) {
          totalSize += item.length;
        }
      });

      return {
        itemCount: keys.length,
        estimatedSize: `${(totalSize / 1024 / 1024).toFixed(2)} MB`,
        userId: this.currentUserId
      };
    } catch (error: any) {
      console.error('[Cache] Failed to get cache info:', error.message);
      return { itemCount: 0, estimatedSize: '0 MB', userId: this.currentUserId };
    }
  }

  // Get cache info for all users
  getAllUsersCacheInfo(): { itemCount: number; estimatedSize: string; totalUsers: number } {
    try {
      const keys = Object.keys(localStorage).filter(key => key.startsWith(this.prefix));
      let totalSize = 0;
      const userIds = new Set<string>();

      keys.forEach(key => {
        const item = localStorage.getItem(key);
        if (item) {
          totalSize += item.length;
        }

        // Extract user ID from key pattern: threadflow_${userId}_${table}_${id}
        const keyParts = key.replace(this.prefix, '').split('_');
        if (keyParts.length >= 2) {
          userIds.add(keyParts[0]);
        }
      });

      return {
        itemCount: keys.length,
        estimatedSize: `${(totalSize / 1024 / 1024).toFixed(2)} MB`,
        totalUsers: userIds.size
      };
    } catch (error: any) {
      console.error('[Cache] Failed to get all users cache info:', error.message);
      return { itemCount: 0, estimatedSize: '0 MB', totalUsers: 0 };
    }
  }
}

export const localStorageCache = new LocalStorageCache();

// Global debugging functions
if (typeof window !== 'undefined') {
  (window as any).clearThreadFlowCache = async () => {
    try {
      await localStorageCache.clearAll();
      return 'Cache cleared successfully for current user';
    } catch (error: any) {
      return `Failed to clear cache: ${error.message}`;
    }
  };

  (window as any).clearAllUsersThreadFlowCache = async () => {
    try {
      await localStorageCache.clearAllUsers();
      return 'Cache cleared successfully for all users';
    } catch (error: any) {
      return `Failed to clear all users cache: ${error.message}`;
    }
  };

  (window as any).getThreadFlowCacheInfo = () => {
    return localStorageCache.getCacheInfo();
  };

  (window as any).getAllUsersThreadFlowCacheInfo = () => {
    return localStorageCache.getAllUsersCacheInfo();
  };
}
