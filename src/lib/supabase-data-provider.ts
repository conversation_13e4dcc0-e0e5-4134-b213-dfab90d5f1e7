import { supabase } from './supabaseClient';
import { db } from './db'; // Import Dexie db instance
import { User as AppUserProfile, Workspace, WorkspaceDisplayUser, Section, Channel, Message, DirectMessage, UserClassification, ChannelTopic } from './types'; // Import necessary types
import { UserConversationReadState } from './types';
import { generateDisplayId } from './utils'; // Import generateDisplayId
import { localStorageCache } from './localStorage-cache';

// Hybrid cache operations that try IndexedDB first, then fall back to localStorage
const hybridCache = {
  async getProfile(userId: string): Promise<AppUserProfile | null> {
    try {
      // Intentionally throw to force localStorage fallback
      throw new Error("Forcing IndexedDB fallback for getProfile");
    } catch (error) {
      console.log('[Cache] IndexedDB failed (forced by dev), trying localStorage for profile:', userId);
      return await localStorageCache.getProfile(userId);
    }
  },

  async putProfile(profile: AppUserProfile): Promise<void> {
    try {
      // Intentionally throw to force localStorage fallback
      throw new Error("Forcing IndexedDB fallback for putProfile");
    } catch (error) {
      console.log('[Cache] IndexedDB failed (forced by dev), using localStorage for profile:', profile.id);
      await localStorageCache.putProfile(profile);
    }
  },

  async getUserConversationReadStates(userId: string): Promise<UserConversationReadState[]> {
    try {
      // Intentionally throw to force localStorage fallback
      throw new Error("Forcing IndexedDB fallback for getUserConversationReadStates");
    } catch (error) {
      console.log('[Cache] IndexedDB failed (forced by dev), trying localStorage for read states:', userId);
      return await localStorageCache.getUserConversationReadStates(userId);
    }
  },

  async putUserConversationReadStates(userId: string, states: UserConversationReadState[]): Promise<void> {
    try {
      // Intentionally throw to force localStorage fallback
      throw new Error("Forcing IndexedDB fallback for putUserConversationReadStates");
    } catch (error) {
      console.log('[Cache] IndexedDB failed (forced by dev), using localStorage for read states:', userId);
      await localStorageCache.putUserConversationReadStates(userId, states);
    }
  },

  async getMessagesForChannel(channelId: string): Promise<Message[]> {
    try {
      // Intentionally throw to force localStorage fallback
      throw new Error("Forcing IndexedDB fallback for getMessagesForChannel");
    } catch (error) {
      console.log('[Cache] IndexedDB failed (forced by dev), trying localStorage for channel messages:', channelId);
      return await localStorageCache.getMessagesForChannel(channelId);
    }
  },

  async putMessagesForChannel(channelId: string, messages: Message[]): Promise<void> {
    try {
      // Intentionally throw to force localStorage fallback
      throw new Error("Forcing IndexedDB fallback for putMessagesForChannel");
    } catch (error) {
      console.log('[Cache] IndexedDB failed (forced by dev), using localStorage for channel messages:', channelId);
      await localStorageCache.putMessagesForChannel(channelId, messages);
    }
  },

  async getMessagesForDM(dmId: string): Promise<Message[]> {
    try {
      // Intentionally throw to force localStorage fallback
      throw new Error("Forcing IndexedDB fallback for getMessagesForDM");
    } catch (error) {
      console.log('[Cache] IndexedDB failed (forced by dev), trying localStorage for DM messages:', dmId);
      return await localStorageCache.getMessagesForDM(dmId);
    }
  },

  async putMessagesForDM(dmId: string, messages: Message[]): Promise<void> {
    try {
      // Intentionally throw to force localStorage fallback
      throw new Error("Forcing IndexedDB fallback for putMessagesForDM");
    } catch (error) {
      console.log('[Cache] IndexedDB failed (forced by dev), using localStorage for DM messages:', dmId);
      await localStorageCache.putMessagesForDM(dmId, messages);
    }
  }
};

/**
 * Fetches a user profile from the 'profiles' table, with hybrid caching.
 * @param userId The ID of the user whose profile is to be fetched.
 * @returns A promise that resolves to the user profile or null if not found.
 */
export const getUserProfile = async (userId: string): Promise<AppUserProfile | null> => {
  try {
    // 1. Try to get from hybrid cache (IndexedDB first, then localStorage)
    const cachedProfile = await hybridCache.getProfile(userId);

    if (cachedProfile) {
      // console.log(`[DB] Cache hit for profile: ${userId}`);
      return cachedProfile;
    }
    // console.log(`[DB] Cache miss for profile: ${userId}`);

    // 2. If not in cache, fetch from Supabase
    const { data, error, status } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', userId)
      .single();

    if (error && status !== 406) {
      console.error(`Supabase error in getUserProfile for user ${userId} (specific error):`, error.message);
      return null;
    }

    if (data) {
      const profile = data as AppUserProfile;
      // 3. Store in hybrid cache
      await hybridCache.putProfile(profile);
      // console.log(`[DB] Cached profile: ${userId}`);
      return profile;
    }

    return null;
  } catch (networkOrUnexpectedError: any) {
    console.error(`Network or unexpected error in getUserProfile for user ${userId}:`, networkOrUnexpectedError.message ? networkOrUnexpectedError.message : networkOrUnexpectedError);
    return null;
  }
};

/**
 * Fetches initial workspace data for a user.
 * @param userId The ID of the user.
 * @param targetWorkspaceId Optional specific workspace ID to load. If not provided, loads the user's primary workspace.
 * @returns A promise that resolves to the user's workspace data or null.
 */
export const getInitialWorkspaceDataForUser = async (userId: string, targetWorkspaceId?: string): Promise<Workspace | null> => {
  try {
    // 1. Fetch Workspace Memberships First
    const { data: memberships, error: membershipError } = await supabase
      .from('workspace_users')
      .select('workspace_id, role')
      .eq('user_id', userId);

    if (membershipError) {
      console.error(`Supabase error fetching memberships for user ${userId}:`, membershipError.message);
      throw membershipError;
    }
    if (!memberships || memberships.length === 0) {
      console.log(`User ${userId} is not a member of any workspaces.`);
      return null;
    }

    // 2. Extract Workspace IDs
    const workspaceIds = memberships.map(mem => mem.workspace_id);

    // 3. Fetch Workspace Details Separately
    const { data: workspaceDetailsList, error: workspaceError } = await supabase
      .from('workspaces')
      .select('*')
      .in('id', workspaceIds);

    if (workspaceError) {
      console.error(`Supabase error fetching workspace details for IDs [${workspaceIds.join(', ')}]:`, workspaceError.message);
      throw workspaceError;
    }
    if (!workspaceDetailsList || workspaceDetailsList.length === 0) {
      console.log(`No workspace details found for workspace IDs [${workspaceIds.join(', ')}] for user ${userId}. This might indicate an issue if memberships existed.`);
      return null;
    }

    // Determine which workspace to load
    let currentWorkspaceId: string;
    let primaryWorkspaceData: any;

    if (targetWorkspaceId) {
      // Load specific workspace if requested
      const targetMembership = memberships.find(m => m.workspace_id === targetWorkspaceId);
      if (!targetMembership) {
        console.error(`User ${userId} is not a member of workspace ${targetWorkspaceId}.`);
        return null;
      }
      primaryWorkspaceData = workspaceDetailsList.find(w => w.id === targetWorkspaceId);
      if (!primaryWorkspaceData) {
        console.error(`Workspace details not found for workspace ${targetWorkspaceId}.`);
        return null;
      }
      currentWorkspaceId = targetWorkspaceId;
    } else {
      // Use the first membership's workspace_id as primary (original logic)
      const primaryMembership = memberships[0];
      primaryWorkspaceData = workspaceDetailsList.find(w => w.id === primaryMembership.workspace_id);
      if (!primaryWorkspaceData || typeof primaryWorkspaceData !== 'object' || primaryWorkspaceData === null) {
        console.error("Primary workspace data (details) could not be found for the user's first membership or is not an object.");
        return null;
      }
      currentWorkspaceId = primaryWorkspaceData.id;
    }
    // Note: primaryMembership.role contains the user's role in this specific primaryWorkspace.
    // This information is implicitly handled later when fetching all users for the currentWorkspaceId,
    // as each user's role in that workspace is fetched.

    // Initialize a Map to store all unique users encountered (workspace members + DM participants)
    const allKnownUsersMap = new Map<string, WorkspaceDisplayUser>();

    // 3. Fetch ALL user profiles from the 'profiles' table first
    const { data: allProfilesData, error: allProfilesError } = await supabase
      .from('profiles')
      .select('*');

    if (allProfilesError) {
      console.error(`Supabase error fetching all profiles:`, allProfilesError.message);
      throw allProfilesError;
    }

    if (allProfilesData) {
      allProfilesData.forEach(profile => {
        const baseProfile = profile as AppUserProfile & { avatar_url?: string, full_name?: string };
        const displayUser: WorkspaceDisplayUser = {
          id: baseProfile.id,
          displayId: generateDisplayId(baseProfile.id, 'u-'),
          name: baseProfile.name || baseProfile.full_name || 'Unknown User',
          avatar: baseProfile.avatar_url || baseProfile.avatar || '',
          status: baseProfile.status,
          title: baseProfile.title,
          classification: baseProfile.classification,
          about: baseProfile.about,
          settings: baseProfile.settings,
          workspaceRole: 'member', // Default role, will be updated if user is in current workspace
        };
        if (!allKnownUsersMap.has(displayUser.id)) {
          allKnownUsersMap.set(displayUser.id, displayUser);
        }
      });
    }

    // Fetch workspace-specific roles for users in the current workspace
    const { data: currentWorkspaceMembers, error: cwmError } = await supabase
      .from('workspace_users')
      .select('user_id, role')
      .eq('workspace_id', currentWorkspaceId);

    if (cwmError) {
      console.error(`Supabase error fetching current workspace members for ${currentWorkspaceId}:`, cwmError.message);
      // Non-fatal, proceed with users already fetched, roles might be default
    }

    if (currentWorkspaceMembers) {
      currentWorkspaceMembers.forEach(member => {
        if (allKnownUsersMap.has(member.user_id)) {
          const existingUser = allKnownUsersMap.get(member.user_id)!;
          existingUser.workspaceRole = member.role as 'admin' | 'member';
          allKnownUsersMap.set(member.user_id, existingUser);
        }
        // If a workspace_user entry exists for a user not in allKnownUsersMap (e.g. profile fetch failed partially),
        // we could try to fetch their profile individually here, or log a warning.
        // For now, we assume allProfilesData was comprehensive or RLS on profiles is the source of truth.
      });
    }

    // 4. Fetch sections for this workspace, and their channels with members and associated files
    const { data: sectionsData, error: sectionsError } = await supabase
      .from('sections')
      .select('*, channels(*, channel_members(user_id), files!files_channel_id_fkey(*))') // Corrected files join hint
      .eq('workspace_id', currentWorkspaceId)
      .order('display_order', { ascending: true })
      .order('name', { foreignTable: 'channels', ascending: true });

    if (sectionsError) throw sectionsError;

    const formattedSections: Section[] = await Promise.all(sectionsData?.map(async (s: any) => {
      const channelsWithTopics = s.channels ? await Promise.all(s.channels.map(async (c: any) => {
        let topics: ChannelTopic[] = [];
        try {
          const { data: topicData, error: topicError } = await supabase
            .rpc('get_channel_topics', { p_channel_id: c.id });

          if (topicError) {
            console.error(`Error fetching topics for channel ${c.id}:`, topicError.message);
          } else if (topicData) {
            // Assuming topicData is already in the correct ChannelTopic[] format
            // and RPC returns non-archived by default or handles it.
            topics = topicData.map((topic: any) => ({
                ...topic, // Spread all fields from RPC result
                // Ensure all fields from ChannelTopic type are present, map if necessary
                id: topic.id,
                channel_id: topic.channel_id || c.id, // Ensure channel_id is present
                title: topic.title,
                summary: topic.summary || '', // Convert null to empty string
                created_by: topic.created_by,
                created_at: topic.created_at,
                updated_at: topic.updated_at,
                display_order: topic.display_order,
                is_archived: topic.is_archived,
                archived_at: topic.archived_at,
                archived_by: topic.archived_by,
                displayId: topic.displayId || generateDisplayId(topic.id, 'ct-'),
            }));
          }
        } catch (e: any) {
          console.error(`Exception fetching topics for channel ${c.id}:`, e.message);
        }

        return {
          id: c.id,
          displayId: generateDisplayId(c.id, 'c-'),
          name: c.name,
          description: c.description,
          isPrivate: c.is_private,
          createdAt: c.created_at,
          channelNote: c.channel_note,
          settings: c.settings,
          lastMessageTimestamp: c.last_message_timestamp,
          activeChannelTopicId: c.active_channel_topic_id,
          members: c.channel_members?.map((cm: any) => cm.user_id) || [],
          messages: [],
          threads: {},
          channelTopics: topics,
          files: c.files?.map((f: any) => ({ // Map fetched files
            id: f.id,
            name: f.name,
            type: f.type,
            url: f.url,
            size_bytes: f.size_bytes,
            uploaded_by_user_id: f.uploaded_by_user_id,
            created_at: f.created_at,
            message_id: f.message_id,
            channel_id: f.channel_id,
            is_pinned_in_channel_id: f.is_pinned_in_channel_id,
            displayId: generateDisplayId(f.id, 'file-'),
          })) || [],
        };
      })) : [];

      return {
        id: s.id,
        displayId: generateDisplayId(s.id, 's-'),
        name: s.name,
        channels: channelsWithTopics,
      };
    }) || []);

    // 5. Fetch Direct Message Sessions for the user
    let directMessages: DirectMessage[] = [];
    const { data: userDmLinks, error: userDmLinksError } = await supabase
      .from('direct_message_participants')
      .select('dm_id')
      .eq('user_id', userId);

    if (userDmLinksError) throw userDmLinksError;

    if (userDmLinks && userDmLinks.length > 0) {
      const dmIds = userDmLinks.map(link => link.dm_id);

      const { data: dmSessionsRaw, error: dmError } = await supabase
        .from('direct_message_sessions')
        .select(`
          id,
          name,
          created_at,
          last_message_timestamp,
          direct_message_participants (
            user_id,
            profiles!inner (id, name, avatar_url, status, title, classification, about, settings)
          )
        `)
        .in('id', dmIds);

      if (dmError) throw dmError;

      if (dmSessionsRaw) {
        directMessages = dmSessionsRaw.map((session: any) => {
          const dmParticipantProfiles: WorkspaceDisplayUser[] = session.direct_message_participants
            .map((p: any) => {
              const profile = p.profiles;
              if (!profile) return null;
              const displayUser: WorkspaceDisplayUser = { // Construct as WorkspaceDisplayUser for consistency
                id: profile.id,
                displayId: generateDisplayId(profile.id, 'u-'),
                name: profile.name || 'Unknown User',
                avatar: profile.avatar_url || '',
                status: profile.status,
                title: profile.title,
                classification: profile.classification as UserClassification | undefined,
                about: profile.about,
                settings: profile.settings,
                // workspaceRole might not be relevant for a DM participant if they aren't in the current workspace
                // but WorkspaceDisplayUser requires it. We can set a default or fetch it if necessary.
                // For now, let's assume they might be in the workspace or use a default.
                workspaceRole: allKnownUsersMap.get(profile.id)?.workspaceRole || 'member', // Get role if known, else default
              };
              // Add/update this participant in our global list of users
              if (!allKnownUsersMap.has(displayUser.id)) {
                allKnownUsersMap.set(displayUser.id, displayUser);
              } else { // If user already known (e.g. from workspace members), merge/update if necessary
                const existingUser = allKnownUsersMap.get(displayUser.id)!;
                allKnownUsersMap.set(displayUser.id, { ...existingUser, ...displayUser }); // Prioritize new data if any
              }
              return displayUser;
            })
            .filter(Boolean) as WorkspaceDisplayUser[];

          let dmDisplayName = session.name;
          if (!dmDisplayName && dmParticipantProfiles.length === 2) {
            const otherUser = dmParticipantProfiles.find(p => p.id !== userId);
            dmDisplayName = otherUser ? otherUser.name : 'DM';
          } else if (!dmDisplayName && dmParticipantProfiles.length === 1) {
            dmDisplayName = dmParticipantProfiles[0].name;
          }

          return {
            id: session.id,
            displayId: generateDisplayId(session.id, 'dm-'),
            name: dmDisplayName,
            participants: dmParticipantProfiles.map(p => p.id),
            messages: [],
            threads: {},
            createdAt: session.created_at,
            lastMessageTimestamp: session.last_message_timestamp,
            unreadCount: 0,
          };
        }).filter(Boolean) as DirectMessage[];
      }
    }

    // console.log('[supabase-data-provider] Fetched and formatted directMessages:', directMessages); // Cleaned up

    const finalWorkspaceUsers = Array.from(allKnownUsersMap.values());
    // console.log('[supabase-data-provider] Consolidated workspace.users:', finalWorkspaceUsers.map(u=>({id: u.id, name: u.name}))); // Cleaned up

    // Populate userWorkspaces list
    const userWorkspacesList = workspaceDetailsList?.map(wsDetails => ({
      id: wsDetails.id,
      name: wsDetails.name,
      iconUrl: wsDetails.icon_url,
    })) || [];

    const constructedWorkspace: Workspace = {
      id: currentWorkspaceId,
      displayId: generateDisplayId(currentWorkspaceId, 'w-'),
      name: primaryWorkspaceData.name,
      iconUrl: primaryWorkspaceData.icon_url,
      owner_id: primaryWorkspaceData.owner_id,
      settings: primaryWorkspaceData.settings,
      createdAt: primaryWorkspaceData.created_at,
      users: finalWorkspaceUsers, // Use the consolidated list
      sections: formattedSections,
      directMessages: directMessages,
      currentUserId: userId,
      currentSectionId: formattedSections.length > 0 ? formattedSections[0].id : null,
      currentChannelId: formattedSections.length > 0 && formattedSections[0].channels.length > 0 ? formattedSections[0].channels[0].id : null,
      currentDirectMessageId: null,
      activeThreadId: null,
      userWorkspaces: userWorkspacesList, // Add the list of all user's workspaces
    };

    // Cache the fetched data
    // try {
    //   // Corrected transaction tables: pass as an array
    //   await db.transaction('rw', [db.workspaces, db.profiles, db.sections, db.channels, db.directMessageSessions], async () => {
    //     const workspaceToCache: Partial<Workspace> = {
    //       id: constructedWorkspace.id,
    //       displayId: constructedWorkspace.displayId,
    //       name: constructedWorkspace.name,
    //       iconUrl: constructedWorkspace.iconUrl,
    //       owner_id: constructedWorkspace.owner_id,
    //       settings: constructedWorkspace.settings,
    //       createdAt: constructedWorkspace.createdAt,
    //     };
    //     await db.workspaces.put(workspaceToCache as Workspace);

    //     const profilesToCache = constructedWorkspace.users.map(wdu => {
    //       const { workspaceRole, ...profileData } = wdu;
    //       return profileData as AppUserProfile; // This is Profile in db.ts
    //     });
    //     if (profilesToCache.length > 0) await db.profiles.bulkPut(profilesToCache);

    //     // Cache Sections
    //     const sectionsToCacheDb = constructedWorkspace.sections.map((s, index) => {
    //         const { channels, ...sectionData } = s; // Exclude channels array from section item itself
    //         return {
    //           ...sectionData,
    //           workspace_id: constructedWorkspace.id,
    //           display_order: sectionData.display_order ?? index // Ensure display_order for Dexie
    //         } as Section;
    //     });
    //     if (sectionsToCacheDb.length > 0) await db.sections.bulkPut(sectionsToCacheDb);

    //     // Cache Channels
    //     const channelsToCacheDb = constructedWorkspace.sections.flatMap(s =>
    //         s.channels.map(ch => {
    //             const { messages, threads, channelTopics, files, members, ...channelData } = ch; // Exclude dynamic/large parts
    //             return {
    //                 ...channelData,
    //                 members: members || [], // Ensure members is an array for cache
    //                 section_id: s.id,
    //                 workspace_id: constructedWorkspace.id,
    //                 // Ensure all required fields for Channel type are present, even if empty for cache
    //                 messages: [],
    //                 threads: {},
    //                 channelTopics: [],
    //                 files: [],
    //             } as Channel;
    //         })
    //     );
    //     if (channelsToCacheDb.length > 0) await db.channels.bulkPut(channelsToCacheDb);

    //     const dmsToCacheDb = constructedWorkspace.directMessages.map(dm => {
    //         const { messages, threads, topics, participants, ...dmData } = dm; // Exclude dynamic/large parts
    //         return {
    //           ...dmData,
    //           participants: participants || [], // Ensure participants is an array
    //           messages: [],
    //           threads: {},
    //           topics: [],
    //         } as DirectMessage; // Use DirectMessage from types.ts; structure is compatible with DirectMessageSession in db.ts
    //     });
    //     if (dmsToCacheDb.length > 0) await db.directMessageSessions.bulkPut(dmsToCacheDb);
    //   });
    //   // Successfully cached workspace data
    // } catch (cacheError: any) {
    //   console.error(`[DB] Error caching initial workspace data for user ${userId}:`, cacheError.message, cacheError);
    //   // Do not let caching error fail the main data fetch
    // }
    console.log('[Cache] Skipping IndexedDB caching for initial workspace data. localStorage will be used by hybridCache for individual items.');

    return constructedWorkspace;

  } catch (error: any) {
    // This catch block is for when the primary Supabase fetch in getInitialWorkspaceDataForUser fails.
    // We will log the error and return null, preventing any IndexedDB fallback attempts.
    console.warn(`Supabase fetch failed in getInitialWorkspaceDataForUser for user ${userId}. Error:`, error.message);
    console.log('[Cache] IndexedDB fallback for initial workspace data is disabled. Returning null.');
    return null;
  }
};

/**
 * Fetches messages for a given channel ID.
 * @param channelId The ID of the channel.
 * @returns A promise that resolves to an array of messages or null on error.
 */
export const getMessagesForChannel = async (channelId: string, limit: number = 50, olderThanTimestamp?: string | null, cursorId?: string | null, sinceTimestamp?: string | null): Promise<Message[] | null> => {
  if (!channelId) return []; // Or perhaps null, consistent with error returns
  try {
    let query = supabase
      .from('messages')
      .select(`
        id,
        content,
        timestamp,
        user_id,
        parent_message_id,
        topic_id,
        edited,
        edited_at,
        also_send_to_channel,
        author:profiles!messages_user_id_fkey ( id, name, avatar_url, title, status ),
        reactions_summary:message_reactions_summary(*),
        files!files_message_id_fkey(*)
      `)
      .eq('channel_id', channelId);
      // .is('parent_message_id', null); // Removed to fetch all messages including replies

    if (sinceTimestamp && cursorId) {
      // Fetch newer messages: order by timestamp ASC, id ASC
      // Use separate conditions for better compatibility
      query = query
        .or(`timestamp.gt.${sinceTimestamp},and(timestamp.eq.${sinceTimestamp},id.gt.${cursorId})`)
        .order('timestamp', { ascending: true })
        .order('id', { ascending: true });
    } else if (olderThanTimestamp && cursorId) {
      // Fetch older messages: order by timestamp DESC, id DESC
      // Use separate conditions for better compatibility
      query = query
        .or(`timestamp.lt.${olderThanTimestamp},and(timestamp.eq.${olderThanTimestamp},id.lt.${cursorId})`)
        .order('timestamp', { ascending: false })
        .order('id', { ascending: false });
    } else if (olderThanTimestamp) {
      // Fallback to timestamp-only filtering for backward compatibility
      query = query.lt('timestamp', olderThanTimestamp).order('timestamp', { ascending: false });
    } else if (sinceTimestamp) {
      // Fallback to timestamp-only filtering for newer messages
      query = query.gt('timestamp', sinceTimestamp).order('timestamp', { ascending: true });
    } else {
      // Initial fetch: order by timestamp DESC, id DESC
      query = query.order('timestamp', { ascending: false }).order('id', { ascending: false });
    }

    query = query.limit(limit);

    const { data, error } = await query;

    if (error) {
      console.warn(`Supabase error fetching messages for channel ${channelId}: ${error.message}. Attempting to load from cache.`);
      const cachedMessages = await hybridCache.getMessagesForChannel(channelId);

      if (cachedMessages && cachedMessages.length > 0) {
        // console.log(`[DB Cache] Loaded ${cachedMessages.length} messages for channel ${channelId} from cache.`); // Original log
        // Sort oldest-first for app-context consistency
        return cachedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      } else {
        // console.log(`[DB Cache] No messages found in cache for channel ${channelId}.`); // Original log
        return [];
      }
    }

    // If fetching newer messages, they come in ascending order, so reverse them for chronological display
    const processedData = sinceTimestamp ? data?.reverse() : data;

    const messagesToReturn = processedData?.map((msg: any) => ({
      id: msg.id,
      displayId: generateDisplayId(msg.id, 'm-'),
      content: msg.content,
      timestamp: msg.timestamp,
      userId: msg.user_id,
      author: msg.author, // Use the alias
      channelId: channelId, // Add channelId for caching context
      parentMessageId: msg.parent_message_id,
      threadId: msg.parent_message_id, // Ensure threadId is populated for replies
      topicId: msg.topic_id,
      edited: msg.edited,
      editedAt: msg.edited_at,
      alsoSendToChannel: msg.also_send_to_channel,
      reactions_summary: msg.reactions_summary || [],
      files: msg.files?.map((f: any) => ({
       id: f.id,
       name: f.name,
       type: f.type,
       url: f.url,
       size_bytes: f.size_bytes,
       uploaded_by_user_id: f.uploaded_by_user_id,
       created_at: f.created_at,
       message_id: f.message_id,
       channel_id: f.channel_id,
       is_pinned_in_channel_id: f.is_pinned_in_channel_id,
       displayId: generateDisplayId(f.id, 'file-'),
     })) || [],
   })) || [];

    // Cache fetched messages using hybrid cache
    if (messagesToReturn.length > 0) {
      await hybridCache.putMessagesForChannel(channelId, messagesToReturn);
      // console.log(`[DB] Cached ${messagesToReturn.length} messages for channel ${channelId}`);
    }

    return messagesToReturn;

  } catch (err: any) {
    console.error(`Exception in getMessagesForChannel for ${channelId}:`, err.message);
    return null;
  }
};

/**
 * Fetches messages for a given Direct Message ID.
 * @param dmId The ID of the Direct Message session.
 * @returns A promise that resolves to an array of messages or null on error.
 */
export const getMessagesForDirectMessage = async (dmId: string, limit: number = 50, olderThanTimestamp?: string | null, cursorId?: string | null, sinceTimestamp?: string | null): Promise<Message[] | null> => {
  if (!dmId) return []; // Or perhaps null
  try {
    let query = supabase
      .from('messages')
      .select(`
        id,
        content,
        timestamp,
        user_id,
        parent_message_id,
        topic_id,
        edited,
        edited_at,
        also_send_to_channel,
        author:profiles!messages_user_id_fkey ( id, name, avatar_url, title, status ),
        reactions_summary:message_reactions_summary(*),
        files!files_message_id_fkey(*)
      `)
      .eq('dm_id', dmId);
      // .is('parent_message_id', null); // Removed to fetch all messages including replies

    if (sinceTimestamp && cursorId) {
      // Fetch newer messages: order by timestamp ASC, id ASC
      // Use separate conditions for better compatibility
      query = query
        .or(`timestamp.gt.${sinceTimestamp},and(timestamp.eq.${sinceTimestamp},id.gt.${cursorId})`)
        .order('timestamp', { ascending: true })
        .order('id', { ascending: true });
    } else if (olderThanTimestamp && cursorId) {
      // Fetch older messages: order by timestamp DESC, id DESC
      // Use separate conditions for better compatibility
      query = query
        .or(`timestamp.lt.${olderThanTimestamp},and(timestamp.eq.${olderThanTimestamp},id.lt.${cursorId})`)
        .order('timestamp', { ascending: false })
        .order('id', { ascending: false });
    } else if (olderThanTimestamp) {
      // Fallback to timestamp-only filtering for backward compatibility
      query = query.lt('timestamp', olderThanTimestamp).order('timestamp', { ascending: false });
    } else if (sinceTimestamp) {
      // Fallback to timestamp-only filtering for newer messages
      query = query.gt('timestamp', sinceTimestamp).order('timestamp', { ascending: true });
    } else {
      // Initial fetch: order by timestamp DESC, id DESC
      query = query.order('timestamp', { ascending: false }).order('id', { ascending: false });
    }

    query = query.limit(limit);

    const { data, error } = await query;

    if (error) {
      console.warn(`Supabase error fetching messages for DM ${dmId}: ${error.message}. Attempting to load from cache.`);
      const cachedMessages = await hybridCache.getMessagesForDM(dmId);

      if (cachedMessages && cachedMessages.length > 0) {
        // console.log(`[DB Cache] Loaded ${cachedMessages.length} messages for DM ${dmId} from cache.`); // Original log
        // Sort oldest-first for app-context consistency
        return cachedMessages.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime());
      } else {
        // console.log(`[DB Cache] No messages found in cache for DM ${dmId}.`); // Original log
        return [];
      }
    }

    // If fetching newer messages, they come in ascending order, so reverse them for chronological display
    const processedData = sinceTimestamp ? data?.reverse() : data;

    const messagesToReturn = processedData?.map((msg: any) => ({
      id: msg.id,
      displayId: generateDisplayId(msg.id, 'm-'),
      content: msg.content,
      timestamp: msg.timestamp,
      userId: msg.user_id,
      author: msg.author, // Use the alias
      dmId: dmId, // Add dmId for caching context
      parentMessageId: msg.parent_message_id,
      threadId: msg.parent_message_id, // Ensure threadId is populated for replies
      topicId: msg.topic_id,
      edited: msg.edited,
      editedAt: msg.edited_at,
      alsoSendToChannel: msg.also_send_to_channel,
      reactions_summary: msg.reactions_summary || [],
      files: msg.files?.map((f: any) => ({
       id: f.id,
       name: f.name,
       type: f.type,
       url: f.url,
       size_bytes: f.size_bytes,
       uploaded_by_user_id: f.uploaded_by_user_id,
       created_at: f.created_at,
       message_id: f.message_id,
       channel_id: f.channel_id,
       is_pinned_in_channel_id: f.is_pinned_in_channel_id,
       displayId: generateDisplayId(f.id, 'file-'),
     })) || [],
      // Note: msg.profiles will be available with richer info if needed.
    })) || [];

    // Cache fetched messages using hybrid cache
    if (messagesToReturn.length > 0) {
      await hybridCache.putMessagesForDM(dmId, messagesToReturn);
      // console.log(`[DB] Cached ${messagesToReturn.length} messages for DM ${dmId}`);
    }
    return messagesToReturn;

  } catch (err: any) {
    console.error(`Exception in getMessagesForDirectMessage for ${dmId}:`, err.message);
    return null;
  }
};

/**
 * Fetches all user conversation read states for a given user.
 * @param userId The ID of the user.
 * @returns A promise that resolves to an array of UserConversationReadState objects or null if an error occurs.
 */
export async function getUserConversationReadStates(userId: string): Promise<UserConversationReadState[] | null> {
  try {
    // 1. Try to get from hybrid cache
    const cachedStates = await hybridCache.getUserConversationReadStates(userId);

    if (cachedStates && cachedStates.length > 0) {
      // console.log(`[DB] Cache hit for userConversationReadStates: ${userId}`);
      return cachedStates;
    }
    // console.log(`[DB] Cache miss for userConversationReadStates: ${userId}`);

    // 2. If not in cache, fetch from Supabase
    const { data, error } = await supabase
      .from('user_conversation_read_states')
      .select('*')
      .eq('user_id', userId);

    if (error) {
      console.error('Error fetching user conversation read states:', error);
      return null;
    }

    const states = data as UserConversationReadState[];
    // 3. Store in hybrid cache
    if (states.length > 0) {
      await hybridCache.putUserConversationReadStates(userId, states);
      // console.log(`[DB] Cached ${states.length} userConversationReadStates for ${userId}`);
    }
    return states;

  } catch (err: any) {
    console.error('Exception in getUserConversationReadStates:', err.message);
    return null;
  }
}

export const getChannelTopics = async (channelId: string, fetchArchived: boolean = false): Promise<ChannelTopic[] | null> => {
  if (!channelId) return null;

  try {
    // 1. Try to get from IndexedDB (filter by archived status if needed, or fetch all and filter in app)
    // For simplicity, we'll fetch all for the channel and let the app filter if `fetchArchived` is false,
    // or rely on the RPC to filter if it supports it.
    // If RPC doesn't support filtering, and we need to cache specifically non-archived, this logic would need adjustment.
    const cachedTopics = await db.channelTopics.where({ channel_id: channelId }).toArray();
    if (cachedTopics.length > 0) {
      // console.log(`[DB] Cache hit for channel topics: ${channelId}, found ${cachedTopics.length}`);
      return fetchArchived ? cachedTopics : cachedTopics.filter(t => !t.is_archived);
    }
    // console.log(`[DB] Cache miss for channel topics: ${channelId}`);

    // 2. Fetch from Supabase using RPC
    // The RPC `get_channel_topics` might already filter out archived topics by default.
    // If it takes a parameter to include archived, we could pass `fetchArchived`.
    // For now, assuming it returns all or non-archived based on its definition.
    // Let's assume the RPC `get_channel_topics` can take `p_include_archived`
    const { data: rpcData, error: rpcError } = await supabase
      .rpc('get_channel_topics', {
        p_channel_id: channelId,
        // p_include_archived: fetchArchived // Assuming RPC supports this parameter
      });

    if (rpcError) {
      console.error(`Supabase RPC error in getChannelTopics for channel ${channelId}:`, rpcError.message);
      // Do not return null immediately, try to proceed if there's partial data or handle error appropriately
      // For now, if RPC fails, we return null as no data can be processed.
      return null;
    }

    if (!rpcData) {
      // console.log(`No topics returned from RPC for channel ${channelId}.`);
      await db.channelTopics.where({ channel_id: channelId }).delete(); // Clear cache if remote is empty
      return [];
    }

    const topics: ChannelTopic[] = rpcData.map((topic: any) => ({
      id: topic.id,
      channel_id: topic.channel_id || channelId,
      title: topic.title,
      summary: topic.summary || '', // Convert null to empty string
      created_by: topic.created_by,
      created_at: topic.created_at,
      updated_at: topic.updated_at,
      display_order: topic.display_order,
      is_archived: topic.is_archived,
      archived_at: topic.archived_at,
      archived_by: topic.archived_by,
      displayId: topic.displayId || generateDisplayId(topic.id, 'ct-'),
    }));

    // 3. Store in IndexedDB
    if (topics.length > 0) {
      // We are caching all topics returned by the RPC.
      // If the RPC returns only non-archived, then only non-archived are cached.
      // If it returns all, then all are cached.
      await db.channelTopics.bulkPut(topics);
      // console.log(`[DB] Cached ${topics.length} channel topics for ${channelId}`);
    } else {
      // If RPC returns no topics, ensure cache for this channel is cleared.
      await db.channelTopics.where({ channel_id: channelId }).delete();
      // console.log(`[DB] Cleared cached topics for channel ${channelId} as RPC returned none.`);
    }

    // Return based on fetchArchived, assuming RPC might return all and we filter here if not done by RPC
    return fetchArchived ? topics : topics.filter(t => !t.is_archived);

  } catch (error: any) {
    console.error(`Exception in getChannelTopics for channel ${channelId}:`, error.message);
    return null;
  }
};
