// Emoji data and utilities for the reaction system

export interface EmojiCategory {
  name: string;
  emojis: string[];
}

export interface EmojiData {
  emoji: string;
  name: string;
  keywords: string[];
}

// Default workspace emoji set - commonly used reactions
export const DEFAULT_REACTION_EMOJIS = ['👍', '❤️', '🎉'];

// Comprehensive emoji data organized by categories
export const EMOJI_CATEGORIES: EmojiCategory[] = [
  {
    name: 'Frequently Used',
    emojis: ['👍', '❤️', '🎉', '👎', '😂', '😮', '😢', '🔥', '💯', '✅', '❌', '👀']
  },
  {
    name: 'Smileys & People',
    emojis: [
      '😀', '😃', '😄', '😁', '😆', '😅', '🤣', '😂', '🙂', '🙃', '😉', '😊',
      '😇', '🥰', '😍', '🤩', '😘', '😗', '😚', '😙', '😋', '😛', '😜', '🤪',
      '😝', '🤑', '🤗', '🤭', '🤫', '🤔', '🤐', '🤨', '😐', '😑', '😶', '😏',
      '😒', '🙄', '😬', '🤥', '😔', '😪', '🤤', '😴', '😷', '🤒', '🤕', '🤢',
      '🤮', '🤧', '🥵', '🥶', '🥴', '😵', '🤯', '🤠', '🥳', '😎', '🤓', '🧐'
    ]
  },
  {
    name: 'Gestures & Body',
    emojis: [
      '👍', '👎', '👌', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕',
      '👇', '☝️', '👋', '🤚', '🖐️', '✋', '🖖', '👏', '🙌', '🤲', '🤝', '🙏'
    ]
  },
  {
    name: 'Hearts & Emotions',
    emojis: [
      '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '🤍', '🤎', '💔', '❣️', '💕',
      '💞', '💓', '💗', '💖', '💘', '💝', '💟', '♥️', '💯', '💢', '💥', '💫'
    ]
  },
  {
    name: 'Objects & Symbols',
    emojis: [
      '🎉', '🎊', '🔥', '✨', '⭐', '🌟', '💥', '💯', '✅', '❌', '❗', '❓',
      '💡', '🔔', '🔕', '📢', '📣', '💬', '💭', '🗯️', '💤', '💨', '👁️', '🗨️'
    ]
  }
];

// Flattened emoji data with search keywords
export const EMOJI_DATA: EmojiData[] = [
  // Frequently Used
  { emoji: '👍', name: 'thumbs up', keywords: ['thumbs', 'up', 'like', 'approve', 'good', 'yes'] },
  { emoji: '👎', name: 'thumbs down', keywords: ['thumbs', 'down', 'dislike', 'bad', 'no'] },
  { emoji: '❤️', name: 'red heart', keywords: ['heart', 'love', 'like', 'red'] },
  { emoji: '😂', name: 'face with tears of joy', keywords: ['laugh', 'funny', 'lol', 'joy', 'tears'] },
  { emoji: '😮', name: 'face with open mouth', keywords: ['surprised', 'wow', 'shock', 'open'] },
  { emoji: '😢', name: 'crying face', keywords: ['sad', 'cry', 'tears', 'upset'] },
  { emoji: '🎉', name: 'party popper', keywords: ['party', 'celebration', 'congrats', 'celebrate'] },
  { emoji: '🔥', name: 'fire', keywords: ['fire', 'hot', 'lit', 'awesome', 'cool'] },
  { emoji: '💯', name: 'hundred points', keywords: ['hundred', 'perfect', 'score', 'complete'] },
  { emoji: '✅', name: 'check mark', keywords: ['check', 'done', 'complete', 'yes', 'correct'] },
  { emoji: '❌', name: 'cross mark', keywords: ['cross', 'no', 'wrong', 'error', 'cancel'] },
  { emoji: '👀', name: 'eyes', keywords: ['eyes', 'look', 'see', 'watch', 'observe'] },

  // Common smileys
  { emoji: '😀', name: 'grinning face', keywords: ['happy', 'smile', 'grin'] },
  { emoji: '😃', name: 'grinning face with big eyes', keywords: ['happy', 'smile', 'joy'] },
  { emoji: '😄', name: 'grinning face with smiling eyes', keywords: ['happy', 'smile', 'laugh'] },
  { emoji: '😁', name: 'beaming face with smiling eyes', keywords: ['happy', 'smile', 'grin'] },
  { emoji: '😆', name: 'grinning squinting face', keywords: ['laugh', 'happy', 'smile'] },
  { emoji: '😅', name: 'grinning face with sweat', keywords: ['laugh', 'sweat', 'relief'] },
  { emoji: '🤣', name: 'rolling on the floor laughing', keywords: ['laugh', 'rofl', 'funny'] },
  { emoji: '🙂', name: 'slightly smiling face', keywords: ['smile', 'happy', 'slight'] },
  { emoji: '🙃', name: 'upside-down face', keywords: ['upside', 'down', 'silly'] },
  { emoji: '😉', name: 'winking face', keywords: ['wink', 'flirt', 'joke'] },
  { emoji: '😊', name: 'smiling face with smiling eyes', keywords: ['happy', 'smile', 'blush'] },
  { emoji: '😇', name: 'smiling face with halo', keywords: ['angel', 'innocent', 'halo'] },
  { emoji: '🥰', name: 'smiling face with hearts', keywords: ['love', 'hearts', 'adore'] },
  { emoji: '😍', name: 'smiling face with heart-eyes', keywords: ['love', 'heart', 'eyes', 'adore'] },
  { emoji: '🤩', name: 'star-struck', keywords: ['star', 'struck', 'amazed', 'wow'] },
  { emoji: '😘', name: 'face blowing a kiss', keywords: ['kiss', 'love', 'blow'] },
  { emoji: '😗', name: 'kissing face', keywords: ['kiss', 'love'] },
  { emoji: '😚', name: 'kissing face with closed eyes', keywords: ['kiss', 'love', 'closed', 'eyes'] },
  { emoji: '😙', name: 'kissing face with smiling eyes', keywords: ['kiss', 'love', 'smile'] },
  { emoji: '😋', name: 'face savoring food', keywords: ['yum', 'delicious', 'food', 'taste'] },
  { emoji: '😛', name: 'face with tongue', keywords: ['tongue', 'silly', 'playful'] },
  { emoji: '😜', name: 'winking face with tongue', keywords: ['wink', 'tongue', 'silly'] },
  { emoji: '🤪', name: 'zany face', keywords: ['crazy', 'silly', 'wild'] },
  { emoji: '😝', name: 'squinting face with tongue', keywords: ['tongue', 'silly', 'playful'] },
  { emoji: '🤑', name: 'money-mouth face', keywords: ['money', 'rich', 'cash'] },
  { emoji: '🤗', name: 'hugging face', keywords: ['hug', 'embrace', 'love'] },
  { emoji: '🤭', name: 'face with hand over mouth', keywords: ['oops', 'surprise', 'secret'] },
  { emoji: '🤫', name: 'shushing face', keywords: ['shush', 'quiet', 'secret'] },
  { emoji: '🤔', name: 'thinking face', keywords: ['think', 'consider', 'hmm'] },
  { emoji: '🤐', name: 'zipper-mouth face', keywords: ['zip', 'quiet', 'secret'] },
  { emoji: '🤨', name: 'face with raised eyebrow', keywords: ['skeptical', 'suspicious', 'doubt'] },
  { emoji: '😐', name: 'neutral face', keywords: ['neutral', 'meh', 'blank'] },
  { emoji: '😑', name: 'expressionless face', keywords: ['blank', 'meh', 'whatever'] },
  { emoji: '😶', name: 'face without mouth', keywords: ['speechless', 'silent', 'no', 'mouth'] },
  { emoji: '😏', name: 'smirking face', keywords: ['smirk', 'smug', 'sly'] },
  { emoji: '😒', name: 'unamused face', keywords: ['unamused', 'meh', 'whatever'] },
  { emoji: '🙄', name: 'face with rolling eyes', keywords: ['roll', 'eyes', 'whatever'] },
  { emoji: '😬', name: 'grimacing face', keywords: ['grimace', 'awkward', 'oops'] },
  { emoji: '🤥', name: 'lying face', keywords: ['lie', 'liar', 'pinocchio'] },
  { emoji: '😔', name: 'pensive face', keywords: ['sad', 'pensive', 'thoughtful'] },
  { emoji: '😪', name: 'sleepy face', keywords: ['sleepy', 'tired', 'sleep'] },
  { emoji: '🤤', name: 'drooling face', keywords: ['drool', 'hungry', 'want'] },
  { emoji: '😴', name: 'sleeping face', keywords: ['sleep', 'tired', 'zzz'] },
  { emoji: '😷', name: 'face with medical mask', keywords: ['sick', 'mask', 'medical'] },
  { emoji: '🤒', name: 'face with thermometer', keywords: ['sick', 'fever', 'ill'] },
  { emoji: '🤕', name: 'face with head-bandage', keywords: ['hurt', 'injured', 'bandage'] },
  { emoji: '🤢', name: 'nauseated face', keywords: ['sick', 'nausea', 'gross'] },
  { emoji: '🤮', name: 'face vomiting', keywords: ['sick', 'vomit', 'gross'] },
  { emoji: '🤧', name: 'sneezing face', keywords: ['sneeze', 'sick', 'achoo'] },
  { emoji: '🥵', name: 'hot face', keywords: ['hot', 'heat', 'sweat'] },
  { emoji: '🥶', name: 'cold face', keywords: ['cold', 'freeze', 'chill'] },
  { emoji: '🥴', name: 'woozy face', keywords: ['dizzy', 'drunk', 'confused'] },
  { emoji: '😵', name: 'dizzy face', keywords: ['dizzy', 'confused', 'knocked', 'out'] },
  { emoji: '🤯', name: 'exploding head', keywords: ['mind', 'blown', 'explode', 'shock'] },
  { emoji: '🤠', name: 'cowboy hat face', keywords: ['cowboy', 'hat', 'western'] },
  { emoji: '🥳', name: 'partying face', keywords: ['party', 'celebrate', 'fun'] },
  { emoji: '😎', name: 'smiling face with sunglasses', keywords: ['cool', 'sunglasses', 'awesome'] },
  { emoji: '🤓', name: 'nerd face', keywords: ['nerd', 'geek', 'smart'] },
  { emoji: '🧐', name: 'face with monocle', keywords: ['monocle', 'fancy', 'inspect'] }
];

// Utility functions
export function searchEmojis(query: string): EmojiData[] {
  if (!query.trim()) return EMOJI_DATA.slice(0, 24); // Return first 24 if no query

  const lowercaseQuery = query.toLowerCase();
  return EMOJI_DATA.filter(emoji =>
    emoji.name.toLowerCase().includes(lowercaseQuery) ||
    emoji.keywords.some(keyword => keyword.toLowerCase().includes(lowercaseQuery))
  );
}

export function getDefaultReactionEmojis(workspaceSettings?: { defaultReactionEmojis?: string[] }): string[] {
  return workspaceSettings?.defaultReactionEmojis || DEFAULT_REACTION_EMOJIS;
}

export function isValidEmoji(emoji: string): boolean {
  // Basic check for emoji - in a real app you might want more sophisticated validation
  return emoji.length > 0 && emoji.length <= 4;
}
