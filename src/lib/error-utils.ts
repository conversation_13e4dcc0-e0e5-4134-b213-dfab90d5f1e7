import { toast } from 'sonner';

/**
 * Common Supabase error codes and their meanings
 */
export const SUPABASE_ERROR_CODES = {
  RLS_VIOLATION: '42501',
  PERMISSION_DENIED: '42501',
  INSUFFICIENT_PRIVILEGE: '42501',
  FOREI<PERSON><PERSON>_KEY_VIOLATION: '23503',
  UNIQUE_VIOLATION: '23505',
  NOT_NULL_VIOLATION: '23502',
  CHECK_VIOLATION: '23514',
} as const;

/**
 * Context-specific error messages for different operations
 */
export const OPERATION_CONTEXTS = {
  CREATE_SECTION: 'create sections',
  CREATE_CHANNEL: 'create channels',
  UPDATE_SECTION: 'update sections',
  DELETE_SECTION: 'delete sections',
  UPDATE_WORKSPACE_SETTINGS: 'modify workspace settings',
  SEND_MESSAGE: 'send messages',
  CREATE_TOPIC: 'create topics',
  UPDATE_PROFILE: 'update profiles',
  CREATE_DM_SESSION: 'create direct message sessions',
  CREATE_CHANNEL_TOPIC: 'create channel topics',
  UPDATE_CHANNEL_TOPIC: 'update channel topics',
  DELETE_CHANNEL_TOPIC: 'delete channel topics',
  ARCHIVE_CHANNEL_TOPIC: 'archive channel topics',
  UNARCHIVE_CHANNEL_TOPIC: 'unarchive channel topics',
  UPDATE_CHANNEL_NOTE: 'update channel note',
  CREATE_WORKSPACE: 'create workspaces',
} as const;

export type OperationContext = typeof OPERATION_CONTEXTS[keyof typeof OPERATION_CONTEXTS];

/**
 * Checks if an error is a privilege/permission related error
 */
export function isPrivilegeError(error: any): boolean {
  if (!error) return false;
  
  // Check error code
  if (error.code === SUPABASE_ERROR_CODES.RLS_VIOLATION) {
    return true;
  }
  
  // Check error message for common privilege-related keywords
  const message = error.message?.toLowerCase() || '';
  return (
    message.includes('row-level security policy') ||
    message.includes('permission denied') ||
    message.includes('insufficient privilege') ||
    message.includes('access denied') ||
    message.includes('not authorized') ||
    message.includes('violates row-level security')
  );
}

/**
 * Gets a user-friendly error message for privilege-related errors
 */
export function getPrivilegeErrorMessage(context: OperationContext): string {
  return `Permission denied: You don't have the necessary rights to ${context} in this workspace. Please contact your workspace administrator.`;
}

/**
 * Gets a user-friendly error message based on the error and context
 */
export function getUserFriendlyErrorMessage(error: any, context: OperationContext, fallbackMessage?: string): string {
  if (isPrivilegeError(error)) {
    return getPrivilegeErrorMessage(context);
  }
  
  // Handle other specific error codes
  switch (error.code) {
    case SUPABASE_ERROR_CODES.UNIQUE_VIOLATION:
      return 'This item already exists. Please choose a different name.';
    case SUPABASE_ERROR_CODES.FOREIGN_KEY_VIOLATION:
      return 'This operation cannot be completed due to related data constraints.';
    case SUPABASE_ERROR_CODES.NOT_NULL_VIOLATION:
      return 'Required information is missing. Please fill in all required fields.';
    case SUPABASE_ERROR_CODES.CHECK_VIOLATION:
      return 'The provided data does not meet the required criteria.';
    default:
      return fallbackMessage || error.message || 'An unexpected error occurred.';
  }
}

/**
 * Handles and displays errors with appropriate user-friendly messages
 */
export function handleSupabaseError(
  error: any,
  context: OperationContext,
  options: {
    fallbackMessage?: string;
    showToast?: boolean;
    logError?: boolean;
    operation?: string;
  } = {}
): string {
  const {
    fallbackMessage,
    showToast = true,
    logError = true,
    operation = 'operation'
  } = options;
  
  if (logError) {
    console.error(`Error during ${operation}:`, error);
  }
  
  const userMessage = getUserFriendlyErrorMessage(error, context, fallbackMessage);
  
  if (showToast) {
    toast.error(userMessage);
  }
  
  return userMessage;
}

/**
 * Handles network and unexpected errors
 */
export function handleUnexpectedError(
  error: any,
  operation: string,
  options: {
    showToast?: boolean;
    logError?: boolean;
  } = {}
): string {
  const { showToast = true, logError = true } = options;
  
  if (logError) {
    console.error(`Unexpected error during ${operation}:`, error);
  }
  
  const message = `An unexpected error occurred during ${operation}. Please try again.`;
  
  if (showToast) {
    toast.error(message);
  }
  
  return message;
}
