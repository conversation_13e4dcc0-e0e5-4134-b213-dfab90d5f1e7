
import React, { useState } from 'react';
import { Channel, File as FileType } from '@/lib/types';
import { useApp } from '@/lib/app-context';
import {
  Files,
  FileText,
  Image,
  Film,
  FileArchive,
  Star,
  PlusCircle,
  Download,
  Filter,
  Search
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';

interface ChannelFilesProps {
  channel: Channel;
}

export const ChannelFiles = ({ channel }: ChannelFilesProps) => {
  const { workspace } = useApp();
  const { toast } = useToast();

  // Helper function to find user by ID from current workspace
  const findUserById = (userId: string) => {
    return workspace?.users.find(u => u.id === userId);
  };
  const [activeTab, setActiveTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');

  // Mock files data
  const mockFiles: FileType[] = channel.files || [
    {
      id: 'file-1',
      name: 'Q3_Project_Plan.pdf',
      type: 'application/pdf',
      url: '#',
      size: 2500000,
      uploadedBy: channel.members[0],
      timestamp: new Date(Date.now() - 86400000 * 3).toISOString(),
      isPinned: true
    },
    {
      id: 'file-2',
      name: 'team-photo.jpg',
      type: 'image/jpeg',
      url: '#',
      size: 1200000,
      uploadedBy: channel.members[1],
      timestamp: new Date(Date.now() - 86400000 * 2).toISOString(),
      isPinned: true
    },
    {
      id: 'file-3',
      name: 'budget_forecast.xlsx',
      type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      url: '#',
      size: 500000,
      uploadedBy: channel.members[0],
      timestamp: new Date(Date.now() - 86400000).toISOString(),
      isPinned: false
    },
    {
      id: 'file-4',
      name: 'presentation.pptx',
      type: 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      url: '#',
      size: 4500000,
      uploadedBy: channel.members[2],
      timestamp: new Date().toISOString(),
      isPinned: false
    },
    {
      id: 'file-5',
      name: 'meeting-recording.mp4',
      type: 'video/mp4',
      url: '#',
      size: 25000000,
      uploadedBy: channel.members[1],
      timestamp: new Date().toISOString(),
      isPinned: false
    },
    {
      id: 'file-6',
      name: 'design_mockups.zip',
      type: 'application/zip',
      url: '#',
      size: 15000000,
      uploadedBy: channel.members[2],
      timestamp: new Date(Date.now() - 86400000 * 1).toISOString(),
      isPinned: false
    },
    {
      id: 'file-7',
      name: 'logo.png',
      type: 'image/png',
      url: '#',
      size: 250000,
      uploadedBy: channel.members[0],
      timestamp: new Date(Date.now() - 86400000 * 4).toISOString(),
      isPinned: true
    }
  ];

  // Filter files based on active tab and search query
  const pinnedFiles = mockFiles.filter(file => file.isPinned);
  const filteredFiles = mockFiles.filter(file =>
    file.name.toLowerCase().includes(searchQuery.toLowerCase()) &&
    (activeTab === 'all' || (activeTab === 'pinned' && file.isPinned))
  );

  // Function to get file icon based on type
  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) return <Image className="text-blue-500" size={20} />;
    if (fileType.startsWith('video/')) return <Film className="text-purple-500" size={20} />;
    if (fileType.includes('spreadsheet') || fileType.includes('excel')) return <FileText className="text-green-500" size={20} />;
    if (fileType.includes('presentation') || fileType.includes('powerpoint')) return <FileText className="text-orange-500" size={20} />;
    if (fileType.includes('zip') || fileType.includes('archive')) return <FileArchive className="text-gray-500" size={20} />;
    return <FileText className="text-red-500" size={20} />;
  };

  // Function to format file size
  const formatFileSize = (bytes: number): string => {
    if (bytes < 1024) return bytes + ' B';
    else if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
    else if (bytes < 1024 * 1024 * 1024) return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    else return (bytes / (1024 * 1024 * 1024)).toFixed(1) + ' GB';
  };

  // Toggle pin status
  const togglePin = (fileId: string) => {
    toast({
      title: "Pin status updated",
      description: "File has been " + (mockFiles.find(f => f.id === fileId)?.isPinned ? "unpinned" : "pinned")
    });
  };

  // Download file (mock)
  const downloadFile = (file: FileType) => {
    toast({
      title: "Download started",
      description: `Downloading ${file.name}`
    });
  };

  // Upload file (mock)
  const uploadFile = () => {
    toast({
      title: "Upload feature",
      description: "File upload functionality would open a file picker dialog"
    });
  };

  // Group files by date
  const groupedFiles: Record<string, FileType[]> = filteredFiles.reduce((groups, file) => {
    const date = new Date(file.timestamp).toLocaleDateString();
    if (!groups[date]) {
      groups[date] = [];
    }
    groups[date].push(file);
    return groups;
  }, {} as Record<string, FileType[]>);

  // Sort dates newest to oldest
  const sortedDates = Object.keys(groupedFiles).sort((a, b) =>
    new Date(b).getTime() - new Date(a).getTime()
  );

  return (
    <div className="h-full flex flex-col">
      <div className="p-4 border-b border-gray-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <Files size={18} />
            <h2 className="font-semibold text-lg">Files</h2>
            <Badge variant="outline" className="ml-1">{mockFiles.length}</Badge>
          </div>

          <Button size="sm" onClick={uploadFile} className="flex items-center gap-1">
            <PlusCircle size={16} className="mr-1" />
            Upload
          </Button>
        </div>

        <div className="flex items-center justify-between gap-4">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="flex-1">
            <TabsList className="bg-transparent h-8 p-0">
              <TabsTrigger
                value="all"
                className="data-[state=active]:bg-gray-100 rounded-md text-sm h-8"
              >
                All Files
              </TabsTrigger>
              <TabsTrigger
                value="pinned"
                className="data-[state=active]:bg-gray-100 rounded-md text-sm h-8"
              >
                <Star size={14} className="mr-1" />
                Pinned ({pinnedFiles.length})
              </TabsTrigger>
            </TabsList>
          </Tabs>

          <div className="relative">
            <Search size={16} className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              placeholder="Search files"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-8 h-8 w-64"
            />
          </div>
        </div>
      </div>

      <ScrollArea className="flex-1 p-4">
        {filteredFiles.length === 0 ? (
          <div className="text-center py-10">
            <p className="text-gray-500">No files found</p>
            {activeTab === 'pinned' && (
              <p className="text-xs text-gray-400 mt-2">
                Pin important files to find them easily
              </p>
            )}
            {searchQuery && (
              <p className="text-xs text-gray-400 mt-2">
                No files match your search query
              </p>
            )}
          </div>
        ) : (
          <div className="space-y-6">
            {sortedDates.map(date => (
              <div key={date}>
                <h3 className="text-xs uppercase font-semibold text-gray-500 mb-2 px-2">{date}</h3>
                <div className="space-y-2">
                  {groupedFiles[date].map(file => {
                    const uploader = findUserById(file.uploadedBy);
                    return (
                      <div key={file.id} className="p-3 rounded-lg border border-gray-200 hover:bg-gray-50 transition-colors">
                        <div className="flex">
                          {/* File icon */}
                          <div className="bg-gray-100 p-2 rounded mr-3">
                            {getFileIcon(file.type)}
                          </div>

                          {/* File details */}
                          <div className="flex-1">
                            <div className="flex items-start justify-between">
                              <div className="flex-1">
                                <h4 className="font-medium text-sm">{file.name}</h4>
                                <div className="flex items-center text-xs text-gray-500 mt-1">
                                  <span>{formatFileSize(file.size)}</span>
                                  <span className="mx-1">•</span>
                                  <span>
                                    {new Date(file.timestamp).toLocaleTimeString([], {
                                      hour: '2-digit',
                                      minute: '2-digit'
                                    })}
                                  </span>
                                </div>
                              </div>

                              <div className="flex gap-1">
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  className="h-8 w-8"
                                  onClick={() => togglePin(file.id)}
                                >
                                  <Star
                                    size={16}
                                    className={file.isPinned ? "fill-yellow-400 text-yellow-400" : ""}
                                  />
                                </Button>
                                <Button
                                  size="icon"
                                  variant="ghost"
                                  className="h-8 w-8"
                                  onClick={() => downloadFile(file)}
                                >
                                  <Download size={16} />
                                </Button>
                              </div>
                            </div>

                            {uploader && (
                              <div className="flex items-center mt-2">
                                <img
                                  src={uploader.avatar}
                                  alt={uploader.name}
                                  className="w-5 h-5 rounded-full mr-1"
                                />
                                <span className="text-xs">{uploader.name}</span>
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );
};
