
import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import {
  Briefcase,
  UserPlus,
  Settings,
  LogOut,
  Plus,
  Users,
  Trash2,
  PenSquare,
  // ShieldCheck, // Added for Workspace Settings icon - Settings icon is fine
} from 'lucide-react';
import { Separator } from '@/components/ui/separator';
import { useApp } from '@/lib/app-context';
import { WorkspaceSettingsDialog } from './WorkspaceSettingsDialog'; // Import the new dialog
import { CreateWorkspaceDialog } from './CreateWorkspaceDialog'; // Import the new dialog
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';

interface WorkspaceMenuProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  initialView?: 'list' | 'create' | 'settings' | 'members' | 'invites';
}

export const WorkspaceMenu: React.FC<WorkspaceMenuProps> = ({
  open,
  onOpenChange,
  initialView,
}) => {
  const { workspace, getCurrentUser, switchWorkspace } = useApp();
  const currentUser = getCurrentUser();

  // Derive displayedWorkspaces directly from the context's userWorkspaces
  const displayedWorkspaces = React.useMemo(() => {
    return workspace.userWorkspaces?.map(ws => ({
      id: ws.id,
      name: ws.name,
      icon: ws.iconUrl || ws.name.substring(0, 2).toUpperCase(), // Use iconUrl if available
    })) || [];
  }, [workspace.userWorkspaces]);

  const [activeView, setActiveView] = useState<
    'list' | 'members' | 'invites' // 'create' and 'settings' are handled by dedicated dialogs
  >('list');
  // const [newWorkspaceName, setNewWorkspaceName] = useState(''); // Removed, handled by CreateWorkspaceDialog
  const [selectedWorkspaceIdForMenu, setSelectedWorkspaceIdForMenu] = useState<string | null>(
    workspace.id
  );
  const [isWsSettingsDialogOpen, setIsWsSettingsDialogOpen] = useState(false); // State for the new dialog

  // Reset state when dialog closes
  useEffect(() => {
    if (open) {
      if (initialView === 'settings') {
        setIsWsSettingsDialogOpen(true);
        setActiveView('list'); // Default to list view for the main menu
      } else if (initialView && ['list', 'members', 'invites'].includes(initialView)) { // 'create' removed
        setActiveView(initialView as 'list' | 'members' | 'invites');
      } else {
        setActiveView('list');
      }
      setSelectedWorkspaceIdForMenu(workspace.id);
    } else {
      // When dialog closes, reset states
      // setIsWsSettingsDialogOpen(false); // Already handled by its own onOpenChange
      setActiveView('list');
      // setNewWorkspaceName(''); // Removed
      // Optionally reset selectedWorkspaceIdForMenu if needed, but usually it's fine to keep last selection
    }
  }, [open, workspace.id, initialView]);

  // const handleCreateWorkspace = () => { // Removed, handled by CreateWorkspaceDialog
  //   if (!newWorkspaceName.trim()) return;
  //   // ... old logic
  //   console.error("Workspace creation not yet implemented. Backend support needed.");
  //   toast.error("Workspace creation is not yet implemented.");
  //   setNewWorkspaceName('');
  //   setActiveView('list');
  // };

  const handleSwitchWorkspace = async (id: string) => {
    if (id === workspace.id) {
      // If clicking the current workspace, just close the menu, unless settings dialog is open
      if (!isWsSettingsDialogOpen) {
        onOpenChange(false);
      }
      return;
    }

    // Find the selected workspace from the displayedWorkspaces (derived from context)
    const selectedWsData = displayedWorkspaces.find(ws => ws.id === id);
    if (!selectedWsData) {
      toast.error(`Workspace with ID ${id} not found.`);
      return;
    }

    setSelectedWorkspaceIdForMenu(id);

    try {
      await switchWorkspace(id); // This will update the context's workspace
      onOpenChange(false); // Close this menu only after successful switch
    } catch (error) {
      // Error handling is done in the switchWorkspace function
      // Reset the selected workspace ID if switching failed
      setSelectedWorkspaceIdForMenu(workspace.id);
    }
  };

  const currentUserIsAdminForActiveWorkspace = () => {
    const userInActiveWorkspace = workspace.users.find(u => u.id === currentUser.id);
    return userInActiveWorkspace?.workspaceRole === 'admin';
  };

  const renderContent = () => {
    // Main workspace list view
    if (activeView === 'list') {
      return (
        <>
          <DialogHeader>
            <DialogTitle>Switch Workspace</DialogTitle>
            <DialogDescription>
              Select a workspace to switch to or create a new one
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4 max-h-[60vh] overflow-y-auto">
            <div className="space-y-2">
              {displayedWorkspaces.map((ws) => (
                <div
                  key={ws.id}
                  className={`flex items-center p-3 rounded-md cursor-pointer transition-colors ${
                    selectedWorkspaceIdForMenu === ws.id
                      ? 'bg-primary/10'
                      : 'hover:bg-accent'
                  }`}
                  onClick={() => handleSwitchWorkspace(ws.id)}
                >
                  <Avatar className="h-10 w-10">
                    <AvatarFallback className="bg-primary text-primary-foreground">
                      {ws.icon}
                    </AvatarFallback>
                  </Avatar>
                  <div className="ml-3 flex-1">
                    <p className="font-medium">{ws.name}</p>
                  </div>
                  {/* Show settings/members buttons only for the *currently active* workspace in the list */}
                  {workspace.id === ws.id && (
                    <div className="flex space-x-1">
                      {currentUserIsAdminForActiveWorkspace() && (
                        <Button
                          variant="ghost"
                          size="icon"
                          title="Workspace Settings"
                          onClick={(e) => {
                            e.stopPropagation();
                            // setActiveView('settings'); // Old behavior
                            setIsWsSettingsDialogOpen(true); // Open new dialog
                            // onOpenChange(false); // Optionally close this menu
                          }}
                        >
                          <Settings size={18} /> {/* Changed back to Settings icon */}
                        </Button>
                      )}
                      <Button
                        variant="ghost"
                        size="icon"
                        onClick={(e) => {
                          e.stopPropagation();
                          setActiveView('members');
                        }}
                      >
                        <Users size={18} />
                      </Button>
                    </div>
                  )}
                </div>
              ))}
            </div>

            <Separator />

            <CreateWorkspaceDialog
              onWorkspaceCreated={async (newWorkspaceId) => {
                // Close the main workspace menu
                onOpenChange(false);
                // The AppContext's createWorkspace already switches to the new workspace
                // so no need to call switchWorkspace here
              }}
            >
              <Button
                variant="outline"
                className="w-full justify-start"
              >
                <Plus size={16} className="mr-2" />
                Create a new workspace
              </Button>
            </CreateWorkspaceDialog>
          </div>
        </>
      );
    }

    // Create new workspace view (REMOVED - now handled by CreateWorkspaceDialog)
    // if (activeView === 'create') { ... }

    // Workspace settings view (REMOVED - now handled by WorkspaceSettingsDialog)
    // if (activeView === 'settings') { ... }

    // Workspace members view
    if (activeView === 'members') {
      // This view should operate on the *active* workspace from context
      const activeWorkspaceContext = workspace;

      return (
        <>
          <DialogHeader>
            <DialogTitle>Workspace Members</DialogTitle>
            <DialogDescription>
              Manage members in {activeWorkspaceContext.name}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4 max-h-[60vh] overflow-y-auto">
            <Button
              variant="outline"
              className="w-full justify-start"
              onClick={() => setActiveView('invites')}
            >
              <UserPlus size={16} className="mr-2" />
              Invite Members
            </Button>

            <Separator />

            <div className="space-y-2">
              <h3 className="text-sm font-medium">Members ({workspace.users.length})</h3>

              {workspace.users.map((user) => (
                <div key={user.id} className="flex items-center p-2 rounded-md">
                  <Avatar className="h-8 w-8">
                    <AvatarImage src={user.avatar} alt={user.name} />
                    <AvatarFallback>{user.name.substring(0, 2).toUpperCase()}</AvatarFallback>
                  </Avatar>
                  <div className="ml-3 flex-1">
                    <p className="text-sm font-medium">{user.name}</p>
                    <p className="text-xs text-muted-foreground">{user.title || 'Member'}</p>
                  </div>
                  {user.id === currentUser.id ? (
                    <span className="text-xs text-muted-foreground">You</span>
                  ) : (
                    <Button variant="ghost" size="icon">
                      <LogOut size={14} />
                    </Button>
                  )}
                </div>
              ))}
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setActiveView('list')}>
              Back
            </Button>
          </DialogFooter>
        </>
      );
    }

    // Invite members view
    if (activeView === 'invites') {
      return (
        <>
          <DialogHeader>
            <DialogTitle>Invite Team Members</DialogTitle>
            <DialogDescription>
              Invite people to join your workspace
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <label htmlFor="email-input" className="text-sm font-medium">
                Email Address
              </label>
              <Input
                id="email-input"
                placeholder="<EMAIL>"
                className="w-full"
                autoFocus
                type="email"
              />
            </div>

            <div className="space-y-2">
              <label htmlFor="role-select" className="text-sm font-medium">
                Role
              </label>
              <select
                id="role-select"
                className="w-full rounded-md border border-input bg-background px-3 py-2"
              >
                <option value="member">Member</option>
                <option value="admin">Admin</option>
              </select>
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setActiveView('members')}>
              Cancel
            </Button>
            <Button>
              <UserPlus size={16} className="mr-2" />
              Send Invite
            </Button>
          </DialogFooter>
        </>
      );
    }

    return null;
  };

  return (
    <>
      <Dialog open={open && !isWsSettingsDialogOpen} onOpenChange={onOpenChange}>
        <DialogContent className="sm:max-w-md">
          {renderContent()}
        </DialogContent>
      </Dialog>
      <WorkspaceSettingsDialog
        open={isWsSettingsDialogOpen}
        onOpenChange={setIsWsSettingsDialogOpen}
      />
    </>
  );
};
