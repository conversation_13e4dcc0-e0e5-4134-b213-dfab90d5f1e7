import React, { useState, useMemo, forwardRef } from 'react';
import { Search } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { EMOJI_CATEGORIES, searchEmojis, getDefaultReactionEmojis } from '@/lib/emoji-data';
import { WorkspaceSettings } from '@/lib/types';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";

interface EmojiPickerProps {
  onEmojiSelect: (emoji: string) => void;
  workspaceSettings?: WorkspaceSettings;
  children: React.ReactNode;
  open?: boolean;
  onOpenChange?: (open: boolean) => void;
}

export const EmojiPicker = forwardRef<HTMLButtonElement, EmojiPickerProps>(({
  onEmojiSelect,
  workspaceSettings,
  children,
  open,
  onOpenChange
}, ref) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [activeTab, setActiveTab] = useState('frequently-used');

  const defaultEmojis = getDefaultReactionEmojis(workspaceSettings);
  
  const searchResults = useMemo(() => {
    if (!searchQuery.trim()) return [];
    return searchEmojis(searchQuery);
  }, [searchQuery]);

  const handleEmojiClick = (emoji: string) => {
    onEmojiSelect(emoji);
    onOpenChange?.(false);
    setSearchQuery(''); // Clear search when emoji is selected
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    if (e.target.value.trim()) {
      setActiveTab('search');
    }
  };

  const renderEmojiGrid = (emojis: string[]) => (
    <div className="grid grid-cols-8 gap-1 p-2">
      {emojis.map((emoji) => (
        <button
          key={emoji}
          onClick={() => handleEmojiClick(emoji)}
          className="flex items-center justify-center w-8 h-8 text-lg rounded hover:bg-[var(--app-hover-bg)] transition-colors duration-100 ease-in-out"
          aria-label={`Select ${emoji}`}
        >
          {emoji}
        </button>
      ))}
    </div>
  );

  const renderSearchResults = () => (
    <div className="p-2">
      {searchResults.length > 0 ? (
        <div className="grid grid-cols-8 gap-1">
          {searchResults.slice(0, 64).map((emojiData) => (
            <button
              key={emojiData.emoji}
              onClick={() => handleEmojiClick(emojiData.emoji)}
              className="flex items-center justify-center w-8 h-8 text-lg rounded hover:bg-[var(--app-hover-bg)] transition-colors duration-100 ease-in-out"
              aria-label={`Select ${emojiData.emoji} (${emojiData.name})`}
              title={emojiData.name}
            >
              {emojiData.emoji}
            </button>
          ))}
        </div>
      ) : (
        <div className="text-center text-[var(--app-secondary-text)] py-8">
          No emojis found for "{searchQuery}"
        </div>
      )}
    </div>
  );

  return (
    <Popover open={open} onOpenChange={onOpenChange}>
      <PopoverTrigger asChild ref={ref}>
        {children}
      </PopoverTrigger>
      <PopoverContent 
        className="w-80 p-0" 
        align="start"
        side="top"
        sideOffset={8}
      >
        <div className="border-b border-[var(--app-secondary-border)] p-3">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[var(--app-secondary-text)] h-4 w-4" />
            <Input
              placeholder="Search emojis..."
              value={searchQuery}
              onChange={handleSearchChange}
              className="pl-10 h-8 text-sm"
            />
          </div>
        </div>

        <Tabs 
          value={searchQuery.trim() ? 'search' : activeTab} 
          onValueChange={setActiveTab}
          className="w-full"
        >
          {!searchQuery.trim() && (
            <TabsList className="grid w-full grid-cols-4 h-8 bg-[var(--app-hover-bg)] rounded-none border-b border-[var(--app-secondary-border)]">
              <TabsTrigger value="frequently-used" className="text-xs h-6">
                Frequent
              </TabsTrigger>
              <TabsTrigger value="smileys" className="text-xs h-6">
                Smileys
              </TabsTrigger>
              <TabsTrigger value="gestures" className="text-xs h-6">
                Gestures
              </TabsTrigger>
              <TabsTrigger value="objects" className="text-xs h-6">
                Objects
              </TabsTrigger>
            </TabsList>
          )}

          <ScrollArea className="h-64">
            {searchQuery.trim() ? (
              renderSearchResults()
            ) : (
              <>
                <TabsContent value="frequently-used" className="mt-0">
                  <div className="p-2">
                    <div className="text-xs font-medium text-[var(--app-secondary-text)] mb-2 px-1">
                      Workspace Defaults
                    </div>
                    {renderEmojiGrid(defaultEmojis)}
                    <div className="text-xs font-medium text-[var(--app-secondary-text)] mb-2 px-1 mt-4">
                      Frequently Used
                    </div>
                    {renderEmojiGrid(EMOJI_CATEGORIES[0].emojis)}
                  </div>
                </TabsContent>

                <TabsContent value="smileys" className="mt-0">
                  {renderEmojiGrid(EMOJI_CATEGORIES[1].emojis)}
                </TabsContent>

                <TabsContent value="gestures" className="mt-0">
                  {renderEmojiGrid(EMOJI_CATEGORIES[2].emojis)}
                </TabsContent>

                <TabsContent value="objects" className="mt-0">
                  <div className="space-y-4">
                    <div>
                      <div className="text-xs font-medium text-[var(--app-secondary-text)] mb-2 px-3">
                        Hearts & Emotions
                      </div>
                      {renderEmojiGrid(EMOJI_CATEGORIES[3].emojis)}
                    </div>
                    <div>
                      <div className="text-xs font-medium text-[var(--app-secondary-text)] mb-2 px-3">
                        Objects & Symbols
                      </div>
                      {renderEmojiGrid(EMOJI_CATEGORIES[4].emojis)}
                    </div>
                  </div>
                </TabsContent>
              </>
            )}
          </ScrollArea>
        </Tabs>
      </PopoverContent>
    </Popover>
  );
});

EmojiPicker.displayName = "EmojiPicker";
