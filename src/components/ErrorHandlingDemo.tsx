import React from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { 
  getUserFriendlyErrorMessage, 
  handleSupabaseError, 
  OPERATION_CONTEXTS 
} from '@/lib/error-utils';

/**
 * Demo component to showcase the improved error handling
 * This is for development/testing purposes only
 */
export const ErrorHandlingDemo: React.FC = () => {
  const demoErrors = [
    {
      name: 'RLS Violation (Section Creation)',
      error: {
        code: '42501',
        message: 'new row violates row-level security policy for table "sections"'
      },
      context: OPERATION_CONTEXTS.CREATE_SECTION
    },
    {
      name: 'Permission Denied (Channel Creation)',
      error: {
        code: '42501',
        message: 'permission denied for table channels'
      },
      context: OPERATION_CONTEXTS.CREATE_CHANNEL
    },
    {
      name: 'Unique Constraint Violation',
      error: {
        code: '23505',
        message: 'duplicate key value violates unique constraint'
      },
      context: OPERATION_CONTEXTS.CREATE_SECTION
    },
    {
      name: 'Generic Database Error',
      error: {
        code: 'UNKNOWN',
        message: 'Some unexpected database error occurred'
      },
      context: OPERATION_CONTEXTS.UPDATE_WORKSPACE_SETTINGS
    }
  ];

  const handleDemoError = (demoError: typeof demoErrors[0]) => {
    // Show the user-friendly message without actually triggering a toast
    const userMessage = getUserFriendlyErrorMessage(
      demoError.error, 
      demoError.context,
      'Fallback error message'
    );
    
    alert(`User-friendly message:\n\n${userMessage}\n\nOriginal error:\n${demoError.error.message}`);
  };

  const handleDemoErrorWithToast = (demoError: typeof demoErrors[0]) => {
    // This will actually show a toast notification
    handleSupabaseError(demoError.error, demoError.context, {
      operation: 'demo operation',
      fallbackMessage: 'Demo error occurred'
    });
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle>Error Handling Improvements Demo</CardTitle>
        <CardDescription>
          This demo shows how privilege-related errors are now handled with user-friendly messages.
          Click the buttons below to see the difference between old and new error messages.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {demoErrors.map((demoError, index) => (
          <div key={index} className="border rounded-lg p-4 space-y-3">
            <h3 className="font-semibold text-sm">{demoError.name}</h3>
            <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded">
              <strong>Original Error:</strong> {demoError.error.message}
            </div>
            <div className="flex gap-2">
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleDemoError(demoError)}
              >
                Show User-Friendly Message
              </Button>
              <Button 
                variant="outline" 
                size="sm"
                onClick={() => handleDemoErrorWithToast(demoError)}
              >
                Show Toast Notification
              </Button>
            </div>
          </div>
        ))}
        
        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-sm mb-2">Key Improvements:</h4>
          <ul className="text-sm space-y-1 text-blue-800">
            <li>• Raw database errors are hidden from users</li>
            <li>• Clear, actionable error messages</li>
            <li>• Context-aware messaging based on the operation</li>
            <li>• Consistent error handling across all operations</li>
            <li>• Guidance on how to resolve permission issues</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};
