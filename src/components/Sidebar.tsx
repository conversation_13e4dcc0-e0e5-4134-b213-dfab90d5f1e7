import React, { useState, useEffect } from 'react';
import { useApp } from '@/lib/app-context';
import { ChevronDown, ChevronRight, Hash, Plus, Users, Settings, MessageSquare, Briefcase, FolderKanban, User, UserRound, LogOut, UserCog, Building2, MessageSquarePlus, Settings2, UserPlus, Lock } from 'lucide-react';
import { ResizablePanelGroup, ResizablePanel, ResizableHandle } from "@/components/ui/resizable";
import { User as UserType } from '@/lib/types';
import { Dialog, DialogContent, DialogFooter, DialogHeader, DialogTitle } from './ui/dialog';
import { Button } from './ui/button';
import { Input } from './ui/input';
import { Switch } from './ui/switch';
import { Label } from './ui/label';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { WorkspaceMenu } from './WorkspaceMenu';
import { WorkspaceSettingsDialog } from './WorkspaceSettingsDialog'; // Import WorkspaceSettingsDialog
import { NewMessageDialog } from './NewMessageDialog';
import { UserPreferencesDialog } from './UserPreferencesDialog'; // Added
import { SetStatusDialog } from './SetStatusDialog'; // Added for status
import { EditProfileDialog } from './EditProfileDialog'; // Added for profile editing

export const Sidebar = () => {
  // Expose the component to window for keyboard shortcuts
  React.useEffect(() => {
    (window as any).__SIDEBAR_COMPONENT__ = {
      setIsAddSectionDialogOpen: (open: boolean) => setIsAddSectionDialogOpen(open)
    };

    // Add event listener for the custom event
    const handleOpenAddSectionDialog = () => {
      setIsAddSectionDialogOpen(true);
    };

    document.querySelector('.app-sidebar')?.addEventListener('openAddSectionDialog', handleOpenAddSectionDialog);

    return () => {
      delete (window as any).__SIDEBAR_COMPONENT__;
      document.querySelector('.app-sidebar')?.removeEventListener('openAddSectionDialog', handleOpenAddSectionDialog);
    };
  }, []);
  const {
    workspace,
    setCurrentChannel,
    setCurrentDirectMessage,
    currentChannel,
    currentDirectMessage,
    currentSection,
    setCurrentSection,
    addChannel, // Changed from addTopic
    addSection,
    addDirectMessage,
    isSidebarOpen,
    getCurrentUser, // Added
    workspaceSettings // Added
  } = useApp();

  const currentUser = getCurrentUser(); // Get current user for settings

  // console.log('[Sidebar] workspace object from context:', workspace); // DEBUG LOG 1 - Cleaned up
  // console.log('[Sidebar] workspace.directMessages from context:', workspace?.directMessages); // DEBUG LOG 2 - Cleaned up


  const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>({});
  const [showDirectMessages, setShowDirectMessages] = useState(true);
  const [isAddSectionHovered, setIsAddSectionHovered] = useState(false);

  const [isAddChannelDialogOpen, setIsAddChannelDialogOpen] = useState(false);
  const [isAddSectionDialogOpen, setIsAddSectionDialogOpen] = useState(false);
  const [isAddTeammateDialogOpen, setIsAddTeammateDialogOpen] = useState(false);
  const [isWorkspaceMenuOpen, setIsWorkspaceMenuOpen] = useState(false);
  const [workspaceMenuInitialView, setWorkspaceMenuInitialView] = useState<'list' | 'invites' | 'create' | 'members' | 'settings' | undefined>('list');
  const [isWorkspaceSettingsDialogOpen, setIsWorkspaceSettingsDialogOpen] = useState(false);
  const [isNewMessageDialogOpen, setIsNewMessageDialogOpen] = useState(false);
  const [isUserPreferencesOpen, setIsUserPreferencesOpen] = useState(false); // Added
  const [isSetStatusDialogOpen, setIsSetStatusDialogOpen] = useState(false); // Added for status
  const [isEditProfileDialogOpen, setIsEditProfileDialogOpen] = useState(false); // Added for profile editing
  const [newChannelName, setNewChannelName] = useState('');
  const [newChannelIsPrivate, setNewChannelIsPrivate] = useState(false);
  const [newSectionName, setNewSectionName] = useState('');
  const [selectedSectionId, setSelectedSectionId] = useState<string | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [sectionHoverId, setSectionHoverId] = useState<string | null>(null);
  const [showSidebarIcons, setShowSidebarIcons] = useState(false); // Default icons off
  const [isResizing, setIsResizing] = useState(false);
  const [sidebarWidth, setSidebarWidth] = useState(() => {
    const saved = localStorage.getItem('sidebarWidth');
    return saved ? parseInt(saved) : 240; // Default width in pixels
  });

  // Handle resize
  const handleMouseDown = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);

    const handleMouseMove = (e: MouseEvent) => {
      // Get the sidebar element
      const sidebar = e.currentTarget as HTMLElement;
      const newWidth = e.clientX;

      // Constrain width between min and max
      const constrainedWidth = Math.max(200, Math.min(newWidth, 480));
      setSidebarWidth(constrainedWidth);
      localStorage.setItem('sidebarWidth', constrainedWidth.toString());
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const isToday = (timestamp: string): boolean => {
    const messageDate = new Date(timestamp);
    const todayDate = new Date();
    return (
      messageDate.getFullYear() === todayDate.getFullYear() &&
      messageDate.getMonth() === todayDate.getMonth() &&
      messageDate.getDate() === todayDate.getDate()
    );
  };

  // Initialize expanded sections
  useEffect(() => {
    const initialExpandedSections: Record<string, boolean> = {};
    workspace.sections.forEach(section => {
      // Expand current section by default, others can be collapsed or based on saved state in future
      initialExpandedSections[section.id] = section.id === workspace.currentSectionId;
    });
    if (workspace.currentSectionId && !initialExpandedSections[workspace.currentSectionId]) {
        initialExpandedSections[workspace.currentSectionId] = true;
    }
    setExpandedSections(initialExpandedSections);
  }, [workspace.sections, workspace.currentSectionId]); // Rerun if sections or currentSectionId changes

  // Update expanded sections when currentSection changes
  useEffect(() => {
    if (currentSection && !expandedSections[currentSection.id]) {
      setExpandedSections(prev => ({
        ...prev,
        [currentSection.id]: true
      }));
    }
  }, [currentSection]);

  const toggleSectionExpanded = (sectionId: string, event?: React.MouseEvent) => {
    if (event) {
      event.stopPropagation();
    }

    setExpandedSections(prev => ({
      ...prev,
      [sectionId]: !prev[sectionId]
    }));
  };

  // Filter out users that already have DMs and current user
  const availableUsers = workspace.users.filter(user => {
    // Skip current user
    if (user.id === workspace.currentUserId) return false;

    // Check if there's already a DM with this user
    const existingDm = workspace.directMessages.find(dm =>
      dm.participants.includes(user.id) &&
      dm.participants.includes(workspace.currentUserId)
    );

    return !existingDm;
  });

  // Filter out current user from direct message list
  // Also, ensure we have the full DM object to access unreadCount
  const directMessageUsers = workspace.directMessages ? workspace.directMessages
    .map(dm => {
      if (!dm || !dm.participants) { // Add null check for dm and dm.participants
        console.warn('[Sidebar] Encountered DM with null or undefined participants:', dm);
        return null;
      }
      // console.log(`[Sidebar DM Map] Processing dm.id: ${dm.id}, participants: ${JSON.stringify(dm.participants)}, currentUserId: ${workspace.currentUserId}`); // Cleaned up
      const otherUserId = dm.participants.find(id => id !== workspace.currentUserId);
      // console.log(`[Sidebar DM Map] otherUserId: ${otherUserId}`); // Cleaned up
      const user = otherUserId ? workspace.users.find(u => u.id === otherUserId) : null;
      if (!user && otherUserId) {
        // console.warn(`[Sidebar DM Map] Profile not found in workspace.users for DM participant ID: ${otherUserId}. workspace.users contains:`, workspace.users.map(u => u.id)); // Cleaned up
      } else if (user) {
        // console.log(`[Sidebar DM Map] Found user profile for ${otherUserId}:`, user.name); // Cleaned up
      }
      return user ? { dm, user } : null;
    })
    .filter(Boolean) as { dm: typeof workspace.directMessages[0], user: UserType }[] : [];

  // Filter out duplicates - keep only the first DM for each user to avoid duplication
  const uniqueDirectMessageUsers = directMessageUsers.filter((dmUser, index, self) =>
    index === self.findIndex((t) => t.user.id === dmUser.user.id)
  );

  // console.log('[Sidebar] Computed directMessageUsers for rendering:', directMessageUsers); // DEBUG LOG 3

  const handleAddChannel = () => { // Renamed from handleAddTopic
    if (newChannelName && selectedSectionId) {
      addChannel(newChannelName, selectedSectionId, newChannelIsPrivate); // Changed from addTopic
      setNewChannelName('');
      setNewChannelIsPrivate(false); // Reset privacy setting
      setIsAddChannelDialogOpen(false);
    }
  };

  const handleAddSection = () => {
    if (newSectionName) {
      addSection(newSectionName);
      setNewSectionName('');
      setIsAddSectionDialogOpen(false);
    }
  };

  const handleAddTeammate = () => {
    if (selectedUserId) {
      addDirectMessage(selectedUserId);
      setSelectedUserId(null);
      setIsAddTeammateDialogOpen(false);
    }
  };

  // Handle resize functionality
  const handleResize = (e: MouseEvent) => {
    if (e.clientX > 200 && e.clientX < 480) {  // Min 200px, max 480px
      setSidebarWidth(e.clientX);
      localStorage.setItem('sidebarWidth', e.clientX.toString());
    }
  };

  const handleResizeStart = (e: React.MouseEvent) => {
    e.preventDefault();
    setIsResizing(true);

    const handleMouseMove = (e: MouseEvent) => {
      const newWidth = e.clientX;
      const constrainedWidth = Math.max(200, Math.min(newWidth, 480));
      setSidebarWidth(constrainedWidth);
      localStorage.setItem('sidebarWidth', constrainedWidth.toString());
    };

    const handleMouseUp = () => {
      setIsResizing(false);
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  if (!isSidebarOpen) {
    return null;
  }

  return (
    <div
      className="h-full flex flex-col app-sidebar relative"
      style={{ width: `${sidebarWidth}px` }}
    >
      <div
        className={`resize-handle ${isResizing ? 'resizing' : ''}`}
        onMouseDown={handleResizeStart}
      />
      {/* Workspace selector button */}
      <div
        className="px-4 py-3 flex justify-between items-center app-sidebar-header" // Increased left/right padding, removed cursor-pointer from here
      >
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <div id="workspace-trigger" className="flex items-center min-w-0 cursor-pointer" data-workspace-menu> {/* Added cursor-pointer here */}
              <div className="w-6 h-6 rounded workspace-icon-bg flex items-center justify-center text-white text-xs font-medium mr-2 flex-shrink-0"> {/* Added workspace-icon-bg, removed direct bg */}
                {workspace.name.substring(0, 2).toUpperCase()}
              </div>
              <h1
                className="font-bold text-lg workspace-name truncate" // Added truncate
                // onClick logic for navigation can remain if desired, but not essential for dropdown
                title={workspace.name}
              >
                {workspace.name}
              </h1>
              <ChevronDown size={18} className="ml-1 text-[var(--app-text)]/70 flex-shrink-0" />
            </div>
          </DropdownMenuTrigger>
          <DropdownMenuContent
            className="w-56" // Standard width for dropdown
            align="start"
          >
            <DropdownMenuItem onClick={() => console.log(`View details for ${workspace.name}`)}>
              <Briefcase size={16} className="mr-2" />
              View {workspace.name}
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            {(() => {
              const currentUser = workspace.users.find(u => u.id === workspace.currentUserId);
              const isAdmin = currentUser?.workspaceRole === 'admin';

              if (isAdmin) {
                return (
                  <DropdownMenuItem
                    onClick={() => {
                      // Double-check admin status before opening
                      if (isAdmin) {
                        setIsWorkspaceSettingsDialogOpen(true);
                      } else {
                        // Import toast from sonner for error feedback
                        const { toast } = require('sonner');
                        toast.error('Access denied: Only workspace administrators can access workspace settings.');
                      }
                    }}
                    data-workspace-settings
                  >
                    <Settings size={16} className="mr-2" />
                    Workspace Settings
                  </DropdownMenuItem>
                );
              }
              return null;
            })()}
            <DropdownMenuItem onClick={() => { setWorkspaceMenuInitialView('invites'); setIsWorkspaceMenuOpen(true); }}>
              <UserPlus size={16} className="mr-2" /> {/* Assuming UserPlus is imported or available */}
              Invite People
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => setIsAddSectionDialogOpen(true)}>
              <FolderKanban size={16} className="mr-2" />
              Create Section
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => { setWorkspaceMenuInitialView('list'); setIsWorkspaceMenuOpen(true); }}>
              <Building2 size={16} className="mr-2" />
              Switch Workspace
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
        {/* Moved New Message Button outside DropdownMenuTrigger */}
        <Button
          className="p-1 h-7 w-7" // Slightly larger for easier click
          variant="ghost"
          size="icon"
          onClick={(e) => {
            // e.stopPropagation(); // No longer strictly necessary here as it's outside the trigger
            setIsNewMessageDialogOpen(true); // Open New Message Dialog
          }}
          title="New Message"
        >
          <MessageSquarePlus size={16} /> {/* Changed icon and slightly increased size for visibility */}
        </Button>
      </div>
      {/* Removed the separate div for New Message button */}

      <div className="flex-1 overflow-y-auto">
        {/* Sections list */}
        <div className="px-3 py-3"> {/* Increased padding for more breathing room */}
          <ul className="mt-2 space-y-1"> {/* Increased vertical spacing between sections */}
            {workspace.sections.map((section) => (
              <li
                key={section.id}
                className="project-item rounded-md" // CSS class can be renamed to section-item if desired
                onMouseEnter={() => setSectionHoverId(section.id)}
                onMouseLeave={() => setSectionHoverId(null)}
              >
                <div
                  className="flex items-center justify-between cursor-pointer text-[var(--app-text)] py-1.5 rounded-md hover:bg-[var(--app-hover-bg)]" // Removed px-1 from here, relying on child padding
                  onClick={() => {
                    // Ensure this section is set as current for dashboard views.
                    // This is key for the user's request: clicking an active, unfolded project
                    // should select it (for the dashboard) AND fold it.
                    setCurrentSection(section.id);

                    // Always toggle the expansion state.
                    // Interaction with useEffect for currentSection (lines 115-122):
                    // - If this section was already current:
                    //   The useEffect is not re-triggered by setCurrentSection (if ID is same).
                    //   So, toggleSectionExpanded directly controls its expansion state.
                    //   - If expanded, it collapses (fulfills user request).
                    //   - If collapsed, it expands (standard toggle).
                    // - If this section was NOT current (and becomes current due to setCurrentSection):
                    //   The useEffect for currentSection will run. If toggleSectionExpanded
                    //   had collapsed it, the useEffect will re-expand it.
                    //   Thus, a newly selected section will become current and expanded.
                    toggleSectionExpanded(section.id);
                  }}
                >
                  <div className="flex items-center min-w-0">
                    <button
                      className="flex-shrink-0 flex items-center justify-center w-5 h-5 mr-0.5 text-[var(--app-text)]/80 hover:text-[var(--app-text)]" // Adjusted chevron button
                      onClick={(e) => toggleSectionExpanded(section.id, e)}
                    >
                      {expandedSections[section.id] ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                    </button>
                    <div className="flex items-center">
                      {showSidebarIcons && <FolderKanban size={14} className="mr-1.5 flex-shrink-0 text-[var(--app-text)]/70" />}
                      <span
                        className={`text-sm truncate sidebar-section-name ${currentSection?.id === section.id ? 'text-[var(--app-highlight)] font-semibold' : 'font-medium'}`}
                      >
                        {section.name}
                      </span>
                    </div>
                  </div>

                  <div
                    className={`project-hover-actions transition-opacity duration-150 ${
                      sectionHoverId === section.id ? 'opacity-100' : 'opacity-0 pointer-events-none'
                    }`}
                  >
                    <button
                      className="flex items-center justify-center w-6 h-6 rounded-full bg-[var(--app-text)]/10 hover:bg-[var(--app-text)]/20 text-[var(--app-text)]/70 hover:text-[var(--app-text)] transition-all duration-150"
                      onClick={(e) => {
                        e.stopPropagation();
                        setSelectedSectionId(section.id);
                        setIsAddChannelDialogOpen(true);
                      }}
                      title="Add channel"
                      data-add-channel
                    >
                      <Plus size={12} />
                    </button>
                  </div>
                </div>

                {expandedSections[section.id] && (
                  <ul className="mt-1 ml-4 py-0.5 space-y-0.5"> {/* Increased left margin and top margin */}
                    {section.channels
                      .filter((channel) => {
                        // Filter out inaccessible private channels if setting is enabled
                        const hideInaccessible = workspaceSettings?.hideInaccessiblePrivateChannels ?? false;
                        if (hideInaccessible && channel.isPrivate && !channel.members.includes(workspace.currentUserId)) {
                          return false;
                        }
                        return true;
                      })
                      .map((channel) => ( // section.channels was project.channels
                      <li key={channel.id}>
                        <button
                          className={`flex items-center w-full py-1 px-1.5 rounded text-left text-sm ${ // Reduced px-2 to px-1.5
                            currentChannel?.id === channel.id  // currentChannel was currentTopic
                              ? 'bg-[var(--app-active)] text-[var(--app-active-text)] font-medium hover:bg-[var(--app-active)]'
                              : 'text-[var(--app-text)] hover:bg-[var(--app-hover-bg)]'
                          } channel-hover`}
                          onClick={(e) => {
                            e.stopPropagation(); // Stop event propagation
                            setCurrentChannel(channel.id); // setCurrentChannel was setCurrentTopic
                          }}
                        >
                          {showSidebarIcons ? (
                            <MessageSquare size={14} className="mr-1.5 flex-shrink-0 text-current/70" />
                          ) : (
                            channel.isPrivate ?
                              <Lock size={14} className="mr-1.5 flex-shrink-0 text-current/70" /> :
                              <Hash size={14} className="mr-1.5 flex-shrink-0 text-current/70" />
                          )}
                          <span className="truncate flex-1">{channel.name}</span>
                          {(() => {
                            // Determine unread badge display settings
                            let showBadge = workspaceSettings?.showUnreadBadgeDefault ?? true;
                            if (currentUser?.settings?.showUnreadBadgeOverride !== null && currentUser?.settings?.showUnreadBadgeOverride !== undefined) {
                              showBadge = currentUser.settings.showUnreadBadgeOverride;
                            }

                            if (showBadge && channel.unreadCount && channel.unreadCount > 0) {
                              if (channel.isUnreadCountPlaceholder && channel.unreadCount === 1) {
                                return (
                                  <span className="ml-auto mr-1.5 flex items-center justify-center">
                                    <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                                  </span>
                                );
                              }
                              return (
                                <span className="ml-auto mr-1 min-w-[18px] h-[18px] flex items-center justify-center text-xs font-semibold bg-red-500 text-white rounded-full">
                                  {channel.unreadCount}
                                </span>
                              );
                            }

                            // If unread badge is not shown, determine message count display settings
                            let showChannelMessageCount = workspaceSettings?.showMessageCountDefault ?? true;
                            if (currentUser?.settings?.showMessageCountOverride !== null && currentUser?.settings?.showMessageCountOverride !== undefined) {
                              showChannelMessageCount = currentUser.settings.showMessageCountOverride;
                            }

                            if (showChannelMessageCount) {
                              let channelMessageCountType = workspaceSettings?.messageCountTypeDefault ?? 'total';
                              if (currentUser?.settings?.messageCountTypeOverride) {
                                channelMessageCountType = currentUser.settings.messageCountTypeOverride;
                              }

                              let count = 0;
                              if (channelMessageCountType === 'total') {
                                count = channel.messages.length;
                              } else { // 'today'
                                count = channel.messages.filter(msg => isToday(msg.timestamp)).length;
                              }

                              if (false && count > 0) { // Hide message count by default
                                return (
                                  <span className="ml-auto mr-1 px-1.5 text-xs text-[var(--app-text)]/60"> {/* Added px-1.5 for consistent padding */}
                                    {count}
                                  </span>
                                );
                              }
                            }
                            return null;
                          })()}
                        </button>
                      </li>
                    ))}
                    {(() => {
                      // Check if there are any visible channels after filtering
                      const hideInaccessible = workspaceSettings?.hideInaccessiblePrivateChannels ?? false;
                      const visibleChannels = section.channels.filter((channel) => {
                        if (hideInaccessible && channel.isPrivate && !channel.members.includes(workspace.currentUserId)) {
                          return false;
                        }
                        return true;
                      });

                      return visibleChannels.length === 0 && (
                        <li className="mt-0.5">
                          <button
                            className="flex items-center w-full py-1 px-1.5 rounded-md text-left text-sm text-[var(--app-text)]/80 hover:text-[var(--app-text)] hover:bg-[var(--app-hover-bg)] transition-all duration-150 group channel-hover"
                            onClick={(e) => {
                              e.stopPropagation();
                              setSelectedSectionId(section.id);
                              setIsAddChannelDialogOpen(true);
                            }}
                          >
                            <div className="flex items-center justify-center w-4 h-4 mr-2 rounded-full bg-[var(--app-text)]/10 group-hover:bg-[var(--app-text)]/20 transition-colors duration-150 flex-shrink-0">
                              <Plus size={10} className="text-[var(--app-text)]/70 group-hover:text-[var(--app-text)]" />
                            </div>
                            <span className="font-medium">Add channel</span>
                          </button>
                        </li>
                      );
                    })()}
                  </ul>
                )}
              </li>
            ))}
          </ul>
        </div>

        {/* Add Section area - Enhanced for empty workspace onboarding */}
        {workspace.sections.length === 0 ? (
          /* Empty workspace onboarding - Prominent sticky CTA */
          <div className="px-3 py-4">
            <div className="border-2 border-dashed border-[var(--app-text)]/20 rounded-lg p-4 bg-[var(--app-text)]/5">
              <div className="text-center">
                <div className="flex items-center justify-center w-12 h-12 mx-auto mb-3 rounded-full bg-[var(--app-highlight)]/10">
                  <FolderKanban size={20} className="text-[var(--app-highlight)]" />
                </div>
                <h3 className="text-sm font-semibold text-[var(--app-text)] mb-2">
                  Get started with sections & channels
                </h3>
                <p className="text-xs text-[var(--app-text)]/70 mb-4 leading-relaxed">
                  Organize your work by creating sections for different teams, projects, or topics.
                </p>
                <button
                  className="flex items-center justify-center w-full py-2.5 px-3 rounded-md bg-[var(--app-highlight)] text-white hover:bg-[var(--app-highlight)]/90 transition-all duration-150 group font-medium text-sm"
                  onClick={() => setIsAddSectionDialogOpen(true)}
                  title="Create your first section"
                >
                  <Plus size={16} className="mr-2" />
                  Add sections & channels
                </button>
              </div>
            </div>
          </div>
        ) : (
          /* Normal hover-based add section for populated workspaces */
          <div
            className="px-1 py-0.5"
            onMouseEnter={() => setIsAddSectionHovered(true)}
            onMouseLeave={() => setIsAddSectionHovered(false)}
          >
            <div
              className={`px-3 transition-all duration-200 ${
                isAddSectionHovered ? 'opacity-100' : 'opacity-0'
              }`}
            >
              <button
                className="flex items-center w-full py-1 px-1.5 rounded-md text-left text-sm text-[var(--app-text)]/80 hover:text-[var(--app-text)] hover:bg-[var(--app-hover-bg)] transition-all duration-150 group"
                onClick={() => setIsAddSectionDialogOpen(true)}
                title="Create a new section"
              >
                <div className="flex items-center justify-center w-5 h-5 mr-2 rounded-full bg-[var(--app-text)]/10 group-hover:bg-[var(--app-text)]/20 transition-colors duration-150 flex-shrink-0">
                  <Plus size={12} className="text-[var(--app-text)]/70 group-hover:text-[var(--app-text)]" />
                </div>
                <span className="font-medium">Add section</span>
              </button>
            </div>
          </div>
        )}

        {/* Direct Messages section */}
        <div className="px-3 py-2 mt-2"> {/* Added top margin and increased horizontal padding */}
          <div
            className="flex items-center justify-between mb-1 cursor-pointer text-[var(--app-text)] py-1 rounded-md hover:bg-[var(--app-hover-bg)]" // Removed px-1
            onClick={() => setShowDirectMessages(!showDirectMessages)}
          >
            <div className="flex items-center flex-1 min-w-0">
              <div className="flex-shrink-0 flex items-center justify-center w-5 h-5 mr-0.5 text-[var(--app-text)]/80">
                 {showDirectMessages ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
              </div>
              {showSidebarIcons && <Users size={14} className="ml-0.5 mr-1.5 flex-shrink-0 text-[var(--app-text)]/70" />}
              <span className="text-sm font-medium truncate">Direct chats</span>
            </div>
            <div className="project-hover-actions"> {/* Reusing class for consistent spacing */}
              <button
                className="flex items-center justify-center w-full h-full rounded-full bg-[var(--app-text)]/10 hover:bg-[var(--app-text)]/20 text-[var(--app-text)]/70 hover:text-[var(--app-text)] transition-all duration-150 group"
                onClick={(e) => {
                  e.stopPropagation();
                  setIsAddTeammateDialogOpen(true);
                }}
                data-add-dm
                title="Start a direct chat"
              >
                <Plus size={12} />
              </button>
            </div>
          </div>

          {showDirectMessages && (
            <ul className="mt-0.5 ml-3 py-0.5 space-y-0.5"> {/* Reduced ml-4 to ml-3, removed pl-1 */}
              {uniqueDirectMessageUsers.length === 0 && workspace.sections.length === 0 ? (
                /* Enhanced empty state for completely new workspace */
                <li className="mt-2">
                  <div className="text-center py-3 px-2">
                    <Users size={24} className="mx-auto mb-2 text-[var(--app-text)]/40" />
                    <p className="text-xs text-[var(--app-text)]/60 mb-3 leading-relaxed">
                      Start conversations with your teammates
                    </p>
                    <button
                      className="flex items-center justify-center w-full py-2 px-3 rounded-md border border-[var(--app-text)]/20 text-[var(--app-text)]/80 hover:text-[var(--app-text)] hover:bg-[var(--app-hover-bg)] transition-all duration-150 text-sm"
                      onClick={() => setIsAddTeammateDialogOpen(true)}
                    >
                      <Plus size={14} className="mr-2" />
                      Start a chat
                    </button>
                  </div>
                </li>
              ) : (
                uniqueDirectMessageUsers.map(({ dm, user }) => (
                  <li key={dm.id}>
                    <button
                      className={`flex items-center w-full py-1 px-1.5 rounded text-left text-sm ${ // Reduced px-2 to px-1.5
                        currentDirectMessage?.id === dm.id
                          ? 'bg-[var(--app-active)] text-[var(--app-active-text)] font-medium hover:bg-[var(--app-active)]'
                          : 'text-[var(--app-text)] hover:bg-[var(--app-hover-bg)]'
                      } channel-hover`}
                      onClick={(e) => {
                        e.stopPropagation(); // Stop event propagation
                        setCurrentDirectMessage(dm.id);
                      }}
                    >
                      <div className="relative flex-shrink-0 mr-1.5">
                        <img
                          src={user.avatar}
                          alt={user.name}
                          className="w-5 h-5 rounded-full"
                        />
                        {user.status === 'online' && (
                          <div className="absolute bottom-0 right-0 w-2 h-2 bg-green-500 rounded-full border border-[var(--app-sidebar)]"></div>
                        )}
                        {user.status === 'away' && (
                          <div className="absolute bottom-0 right-0 w-2 h-2 bg-yellow-500 rounded-full border border-[var(--app-sidebar)]"></div>
                        )}
                        {user.status === 'busy' && ( // Added busy status indicator (e.g., red)
                          <div className="absolute bottom-0 right-0 w-2 h-2 bg-red-500 rounded-full border border-[var(--app-sidebar)]"></div>
                        )}
                        {user.status === 'dnd' && ( // Added dnd status indicator (e.g., purple or same as busy)
                          <div className="absolute bottom-0 right-0 w-2 h-2 bg-purple-500 rounded-full border border-[var(--app-sidebar)]"></div>
                        )}
                        {user.status === 'offline' && (
                          <div className="absolute bottom-0 right-0 w-2 h-2 bg-gray-400 rounded-full border border-[var(--app-sidebar)]"></div>
                        )}
                      </div>
                      <span className="ml-1 truncate flex-1">{user.name}</span>
                      {(() => {
                        // Determine unread badge display settings
                        let showBadge = workspaceSettings?.showUnreadBadgeDefault ?? true;
                        if (currentUser?.settings?.showUnreadBadgeOverride !== null && currentUser?.settings?.showUnreadBadgeOverride !== undefined) {
                          showBadge = currentUser.settings.showUnreadBadgeOverride;
                        }

                        if (showBadge && dm.unreadCount && dm.unreadCount > 0) {
                          if (dm.isUnreadCountPlaceholder && dm.unreadCount === 1) {
                            return (
                              <span className="ml-auto mr-1.5 flex items-center justify-center">
                                <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                              </span>
                            );
                          }
                          return (
                            <span className="ml-auto mr-1 min-w-[18px] h-[18px] flex items-center justify-center text-xs font-semibold bg-red-500 text-white rounded-full">
                              {dm.unreadCount}
                            </span>
                          );
                        }

                        // If unread badge is not shown, determine message count display settings for DMs
                        let showDmMessageCount = workspaceSettings?.showMessageCountDefault ?? true; // Re-using channel setting name
                        if (currentUser?.settings?.showMessageCountOverride !== null && currentUser?.settings?.showMessageCountOverride !== undefined) {
                          showDmMessageCount = currentUser.settings.showMessageCountOverride;
                        }

                        if (showDmMessageCount) {
                          let dmMessageCountType = workspaceSettings?.messageCountTypeDefault ?? 'total';
                          if (currentUser?.settings?.messageCountTypeOverride) {
                            dmMessageCountType = currentUser.settings.messageCountTypeOverride;
                          }

                          let count = 0;
                          // Assuming dm.messages exists and is an array
                          if (dm.messages) {
                             if (dmMessageCountType === 'total') {
                              count = dm.messages.length;
                            } else { // 'today'
                              count = dm.messages.filter(msg => isToday(msg.timestamp)).length;
                            }
                          }

                          if (false && count > 0) { // Hide message count by default
                            return (
                              <span className="ml-auto mr-1 px-1.5 text-xs text-[var(--app-text)]/60"> {/* Added px-1.5 for consistent padding */}
                                {count}
                              </span>
                            );
                          }
                        }
                        return null;
                      })()}
                    </button>
                  </li>
                ))
              )}
            </ul>
          )}
        </div>
      </div>

      {/* User section at the bottom */}
      {currentUser && (
        <div className="mt-auto user-section"> {/* Removed border-t border-app-border, relying on .user-section style from global-styles.css */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              {/* Removed direct bg, hover, and text-white. Relying on .user-section and its :hover in global-styles.css and --app-text */}
              <div className="flex items-center cursor-pointer p-3 w-full">
                <div className="relative">
                  {currentUser.avatar && (currentUser.avatar.startsWith('http') || currentUser.avatar.startsWith('data:')) ? (
                    <img
                      src={currentUser.avatar}
                      alt={currentUser.name}
                      className="w-7 h-7 rounded-md object-cover" // Added object-cover
                    />
                  ) : (
                    <div className="w-7 h-7 rounded-md bg-muted flex items-center justify-center text-muted-foreground">
                      {currentUser.name ? currentUser.name.substring(0, 1).toUpperCase() : <UserRound size={18} />}
                    </div>
                  )}
                  {/* The small status dot's border should ideally use var(--app-user-section) or var(--app-sidebar) depending on context */}
                  {currentUser.status === 'online' && (
                    <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-green-500 rounded-full border border-[var(--app-user-section)]"></div>
                  )}
                  {currentUser.status === 'away' && (
                    <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-yellow-500 rounded-full border border-[var(--app-user-section)]"></div>
                  )}
                  {currentUser.status === 'busy' && ( // Added busy status indicator
                    <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-red-500 rounded-full border border-[var(--app-user-section)]"></div>
                  )}
                  {currentUser.status === 'dnd' && ( // Added dnd status indicator
                    <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-purple-500 rounded-full border border-[var(--app-user-section)]"></div>
                  )}
                  {currentUser.status === 'offline' && (
                    <div className="absolute -bottom-0.5 -right-0.5 w-3 h-3 bg-gray-400 rounded-full border border-[var(--app-user-section)]"></div>
                  )}
                </div>
                <span className="ml-2.5 text-sm font-medium">
                  {currentUser.name}
                  {currentUser.statusMessage && <span className="text-xs text-muted-foreground ml-1 truncate block max-w-[120px]">({currentUser.statusMessage})</span>}
                </span>
              </div>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              align="end"
              side="top"
              className="w-60 mb-1 bg-popover border-app-border shadow-xl" // Assuming popover, border-app-border are themed correctly by Tailwind or global CSS
              alignOffset={-5}
            >
              <div className="px-3 py-2 text-sm text-popover-foreground">
                <span className="font-bold block">{currentUser.name}</span>
                {currentUser.statusMessage ? (
                  <span className="text-xs text-muted-foreground truncate block max-w-full">{currentUser.statusMessage}</span>
                ) : (
                  <span className="text-xs text-muted-foreground">Signed in</span>
                )}
              </div>
              <DropdownMenuSeparator className="bg-border" />
              <DropdownMenuItem
                className="text-popover-foreground hover:bg-accent focus:bg-accent"
                onClick={() => setIsSetStatusDialogOpen(true)} // Added onClick
              >
                <UserCog size={16} className="mr-2.5 text-muted-foreground" />
                Set status
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-popover-foreground hover:bg-accent focus:bg-accent"
                onClick={() => setIsEditProfileDialogOpen(true)}
              >
                <User size={16} className="mr-2.5 text-muted-foreground" />
                Profile
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-border"/>
              <DropdownMenuItem
                className="text-popover-foreground hover:bg-accent focus:bg-accent"
                onClick={() => setIsWorkspaceMenuOpen(true)}
              >
                <Building2 size={16} className="mr-2.5 text-muted-foreground" />
                Switch workspace
              </DropdownMenuItem>
              <DropdownMenuItem
                className="text-popover-foreground hover:bg-accent focus:bg-accent"
                onClick={() => setIsUserPreferencesOpen(true)} // Added onClick
              >
                <Settings size={16} className="mr-2.5 text-muted-foreground" />
                Preferences
              </DropdownMenuItem>
              <DropdownMenuSeparator className="bg-border"/>
              <DropdownMenuItem className="text-popover-foreground hover:bg-accent focus:bg-accent">
                <LogOut size={16} className="mr-2.5 text-muted-foreground" />
                Sign out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      )}

      {/* Add Channel Dialog */}
      <Dialog open={isAddChannelDialogOpen} onOpenChange={setIsAddChannelDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add a new channel</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div>
              <Input
                placeholder="Channel name (e.g. #general, #random)"
                value={newChannelName}
                onChange={(e) => setNewChannelName(e.target.value)}
                onKeyDown={(e) => { if (e.key === 'Enter' && newChannelName.trim()) handleAddChannel(); }}
                autoFocus
              />
            </div>
            <div className="flex items-center justify-between">
              <div className="space-y-1">
                <Label htmlFor="channel-privacy" className="text-sm font-medium">
                  Make private
                </Label>
                <p className="text-xs text-muted-foreground">
                  {newChannelIsPrivate
                    ? "Only invited members can see and access this channel"
                    : "Anyone in the workspace can see and join this channel"
                  }
                </p>
              </div>
              <Switch
                id="channel-privacy"
                checked={newChannelIsPrivate}
                onCheckedChange={setNewChannelIsPrivate}
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddChannelDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddChannel} disabled={!newChannelName.trim()}>
              {newChannelIsPrivate ? (
                <>
                  <Lock size={16} className="mr-2" />
                  Create Private Channel
                </>
              ) : (
                <>
                  <Hash size={16} className="mr-2" />
                  Create Public Channel
                </>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Section Dialog */}
      <Dialog open={isAddSectionDialogOpen} onOpenChange={setIsAddSectionDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create a new section</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="Section name (e.g. Q4 Planning)"
              value={newSectionName}
              onChange={(e) => setNewSectionName(e.target.value)}
              onKeyDown={(e) => { if (e.key === 'Enter' && newSectionName.trim()) handleAddSection(); }}
              className="mb-4"
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddSectionDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleAddSection} disabled={!newSectionName.trim()}>
              Add Section
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Add Teammate Dialog */}
      <Dialog open={isAddTeammateDialogOpen} onOpenChange={setIsAddTeammateDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Start a direct chat</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            {availableUsers.length > 0 ? (
              <div className="space-y-2">
                {availableUsers.map(user => (
                  <div
                    key={user.id}
                    className={`flex items-center p-2 rounded cursor-pointer ${
                      selectedUserId === user.id ? 'bg-blue-100 dark:bg-blue-900' : 'hover:bg-gray-100 dark:hover:bg-gray-700'
                    }`}
                    onClick={() => {
                      addDirectMessage(user.id);
                      setIsAddTeammateDialogOpen(false);
                    }}
                  >
                    <img
                      src={user.avatar}
                      alt={user.name}
                      className="w-8 h-8 rounded-full mr-3"
                    />
                    <div>
                      <div className="font-medium text-app-text">{user.name}</div>
                      <div className="text-xs text-gray-500 dark:text-gray-400">{user.title}</div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-4 text-gray-500 dark:text-gray-400">
                <Users size={40} className="mx-auto mb-2 text-gray-400" />
                <p>You already have chats with all teammates!</p>
              </div>
            )}
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsAddTeammateDialogOpen(false)}>
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Workspace Menu Dialog */}
      <WorkspaceMenu
        open={isWorkspaceMenuOpen}
        onOpenChange={setIsWorkspaceMenuOpen}
        initialView={workspaceMenuInitialView}
      />

      <WorkspaceSettingsDialog
        open={isWorkspaceSettingsDialogOpen}
        onOpenChange={setIsWorkspaceSettingsDialogOpen}
      />

      {/* New Message Dialog */}
      <NewMessageDialog
        open={isNewMessageDialogOpen}
        onOpenChange={setIsNewMessageDialogOpen}
      />

      {/* User Preferences Dialog */}
      <UserPreferencesDialog
        open={isUserPreferencesOpen}
        onOpenChange={setIsUserPreferencesOpen}
      />

      {/* Set Status Dialog */}
      <SetStatusDialog
        open={isSetStatusDialogOpen}
        onOpenChange={setIsSetStatusDialogOpen}
      />

      {/* Edit Profile Dialog */}
      <EditProfileDialog
        open={isEditProfileDialogOpen}
        onOpenChange={setIsEditProfileDialogOpen}
      />
    </div>
  );
};
