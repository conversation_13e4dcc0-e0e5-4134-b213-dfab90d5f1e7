import React from 'react';
import { Section } from '@/lib/types';
import { useApp } from '@/lib/app-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Progress } from '@/components/ui/progress';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { BarChart3, PieChart, Users, MessageSquare, FileText, BarChart, Clock, Calendar } from 'lucide-react';

interface ProjectAnalyticsProps {
  section: Section;
}

export const ProjectAnalytics: React.FC<ProjectAnalyticsProps> = ({ section }) => {
  const { workspace } = useApp();

  // Helper function to find user by ID from current workspace
  const findUserById = (userId: string) => {
    return workspace?.users.find(u => u.id === userId);
  };
  const [activeTab, setActiveTab] = React.useState('overview');

  // Calculate various metrics
  const calculateMetrics = () => {
    // Total messages across all channels
    let totalMessages = 0;
    // Total messages per channel
    const messagesByChannel: Record<string, number> = {};
    // Total messages per user
    const messagesByUser: Record<string, number> = {};
    // Messages per day of week
    const messagesByDayOfWeek: Record<string, number> = {
      'Sunday': 0,
      'Monday': 0,
      'Tuesday': 0,
      'Wednesday': 0,
      'Thursday': 0,
      'Friday': 0,
      'Saturday': 0
    };
    // Activity by hour
    const messagesByHour: Record<string, number> = {};
    for (let i = 0; i < 24; i++) {
      messagesByHour[i] = 0;
    }

    // Populate the metrics
    section.channels.forEach(channel => {
      messagesByChannel[channel.name] = channel.messages.length;

      channel.messages.forEach(message => {
        totalMessages++;

        // Count by user
        messagesByUser[message.userId] = (messagesByUser[message.userId] || 0) + 1;

        // Count by day of week
        const date = new Date(message.timestamp);
        const dayOfWeek = date.toLocaleDateString('en-US', { weekday: 'long' });
        messagesByDayOfWeek[dayOfWeek] = (messagesByDayOfWeek[dayOfWeek] || 0) + 1;

        // Count by hour
        const hour = date.getHours();
        messagesByHour[hour] = (messagesByHour[hour] || 0) + 1;
      });
    });

    return {
      totalMessages,
      messagesByChannel,
      messagesByUser,
      messagesByDayOfWeek,
      messagesByHour
    };
  };

  const metrics = calculateMetrics();

  // Sort channels by message count
  const sortedChannels = Object.entries(metrics.messagesByChannel)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5); // Top 5

  // Sort users by message count
  const sortedUsers = Object.entries(metrics.messagesByUser)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 5); // Top 5

  // Calculate max values for scaling
  const maxChannelMessages = Math.max(...Object.values(metrics.messagesByChannel));
  const maxUserMessages = Math.max(...Object.values(metrics.messagesByUser));
  const maxDayMessages = Math.max(...Object.values(metrics.messagesByDayOfWeek));
  const maxHourMessages = Math.max(...Object.values(metrics.messagesByHour));

  // Calculate user engagement percentages for the pie chart segments
  const getUserEngagementData = () => {
    // Get total number of messages
    const totalMessages = Object.values(metrics.messagesByUser).reduce((sum, count) => sum + count, 0);

    if (totalMessages === 0) return [];

    // For each user, calculate their percentage of contribution
    // Sort by highest contributors first
    return Object.entries(metrics.messagesByUser)
      .map(([userId, messageCount]) => {
        const user = findUserById(userId);
        const percentage = (messageCount / totalMessages) * 100;
        return {
          userId,
          name: user?.name || 'Unknown',
          messageCount,
          percentage
        };
      })
      .sort((a, b) => b.percentage - a.percentage);
  };

  const userEngagementData = getUserEngagementData();

  // Function to generate SVG pie chart segments
  const generatePieChartSegments = (data: { percentage: number; name: string }[]) => {
    if (data.length === 0) return [];

    let currentAngle = 0;
    const segments = [];
    const colors = ['#4f46e5', '#0ea5e9', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899'];

    for (let i = 0; i < data.length; i++) {
      const item = data[i];
      const angle = (item.percentage / 100) * 360;

      // Don't add tiny slices that are hard to see
      if (angle < 3 && i > 4) continue;

      // Start and end angles
      const startAngle = currentAngle;
      const endAngle = currentAngle + angle;

      // Calculate coordinates
      const startX = 50 + 40 * Math.cos((startAngle * Math.PI) / 180);
      const startY = 50 + 40 * Math.sin((startAngle * Math.PI) / 180);
      const endX = 50 + 40 * Math.cos((endAngle * Math.PI) / 180);
      const endY = 50 + 40 * Math.sin((endAngle * Math.PI) / 180);

      // Flag for large arc
      const largeArcFlag = angle > 180 ? 1 : 0;

      // Create SVG path
      const path = `M 50 50 L ${startX} ${startY} A 40 40 0 ${largeArcFlag} 1 ${endX} ${endY} Z`;

      segments.push({
        path,
        color: colors[i % colors.length],
        percentage: item.percentage,
        name: item.name
      });

      currentAngle += angle;
    }

    return segments;
  };

  const pieChartSegments = generatePieChartSegments(userEngagementData);

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-[var(--app-main-text)]">Project Analytics</h2>
        <p className="text-[var(--app-main-text)]/70">
          Insights and metrics across {section.name}'s channels
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList>
          <TabsTrigger value="overview" className="flex items-center">
            <BarChart3 className="h-4 w-4 mr-2" />
            Overview
          </TabsTrigger>
          <TabsTrigger value="user-activity" className="flex items-center">
            <Users className="h-4 w-4 mr-2" />
            User Activity
          </TabsTrigger>
          <TabsTrigger value="time-analysis" className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            Time Analysis
          </TabsTrigger>
        </TabsList>

        {/* Overview Tab */}
        <TabsContent value="overview" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {/* Key Metrics Card */}
            <Card>
              <CardHeader>
                <CardTitle>Key Metrics</CardTitle>
                <CardDescription>Overall statistics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <MessageSquare className="h-4 w-4 mr-2 text-[var(--app-main-text)]/70" />
                      <span className="text-sm">Total Messages</span>
                    </div>
                    <span className="font-bold">{metrics.totalMessages}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <FileText className="h-4 w-4 mr-2 text-[var(--app-main-text)]/70" />
                      <span className="text-sm">Total Channels</span>
                    </div>
                    <span className="font-bold">{section.channels.length}</span>
                  </div>
                  <div className="flex justify-between items-center">
                    <div className="flex items-center">
                      <Users className="h-4 w-4 mr-2 text-[var(--app-main-text)]/70" />
                      <span className="text-sm">Active Users</span>
                    </div>
                    <span className="font-bold">{Object.keys(metrics.messagesByUser).length}</span>
                  </div>

                  {section.channels.length > 0 && (
                    <div className="flex justify-between items-center">
                      <div className="flex items-center">
                        <MessageSquare className="h-4 w-4 mr-2 text-[var(--app-main-text)]/70" />
                        <span className="text-sm">Avg. Messages/Channel</span>
                      </div>
                      <span className="font-bold">
                        {Math.round(metrics.totalMessages / section.channels.length)}
                      </span>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Channel Activity Card */}
            <Card className="md:col-span-2">
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart className="h-5 w-5 mr-2" />
                  Channel Activity
                </CardTitle>
                <CardDescription>Message distribution across channels</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {sortedChannels.map(([channelName, messageCount]) => (
                    <div key={channelName} className="space-y-1">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">{channelName}</span>
                        <span className="text-sm text-[var(--app-main-text)]/70">{messageCount}</span>
                      </div>
                      <Progress
                        value={maxChannelMessages > 0 ? (messageCount / maxChannelMessages) * 100 : 0}
                        className="h-2"
                      />
                    </div>
                  ))}
                  {sortedChannels.length === 0 && (
                    <div className="text-center py-6 text-[var(--app-main-text)]/70">
                      No channel activity data available
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* User Activity Tab */}
        <TabsContent value="user-activity" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Top Contributors Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Users className="h-5 w-5 mr-2" />
                  Top Contributors
                </CardTitle>
                <CardDescription>Most active users by message count</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {sortedUsers.map(([userId, messageCount]) => {
                    const user = findUserById(userId);

                    return (
                      <div key={userId} className="flex items-center space-x-4">
                        <Avatar className="h-10 w-10">
                          <AvatarImage src={user?.avatar} alt={user?.name || 'User'} />
                          <AvatarFallback>{user?.name?.[0] || 'U'}</AvatarFallback>
                        </Avatar>
                        <div className="flex-1 space-y-1">
                          <div className="flex justify-between">
                            <span className="font-medium">{user?.name || 'Unknown user'}</span>
                            <span className="text-sm text-[var(--app-main-text)]/70">
                              {messageCount} messages
                            </span>
                          </div>
                          <Progress
                            value={maxUserMessages > 0 ? (messageCount / maxUserMessages) * 100 : 0}
                            className="h-2"
                          />
                        </div>
                      </div>
                    );
                  })}
                  {sortedUsers.length === 0 && (
                    <div className="text-center py-6 text-[var(--app-main-text)]/70">
                      No user activity data available
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* User Engagement Distribution Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <PieChart className="h-5 w-5 mr-2" />
                  User Engagement
                </CardTitle>
                <CardDescription>Distribution of participation</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-center">
                  {userEngagementData.length > 0 ? (
                    <div className="space-y-6">
                      <div className="relative w-[220px] h-[220px]">
                        <svg viewBox="0 0 100 100" className="w-full h-full transform -rotate-90">
                          {pieChartSegments.map((segment, index) => (
                            <path
                              key={index}
                              d={segment.path}
                              fill={segment.color}
                              stroke="var(--app-bg)"
                              strokeWidth="1"
                              className="hover:opacity-90 transition-opacity"
                            />
                          ))}
                          <circle cx="50" cy="50" r="30" fill="var(--app-bg)" />
                        </svg>
                      </div>

                      <div className="space-y-2">
                        {pieChartSegments.map((segment, index) => (
                          <div key={index} className="flex items-center justify-between">
                            <div className="flex items-center">
                              <div
                                className="w-3 h-3 mr-2 rounded-sm"
                                style={{ backgroundColor: segment.color }}
                              />
                              <span className="text-sm">{segment.name}</span>
                            </div>
                            <span className="text-sm text-[var(--app-main-text)]/70">
                              {segment.percentage.toFixed(1)}%
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ) : (
                    <div className="h-[300px] flex items-center justify-center text-[var(--app-main-text)]/70">
                      <p>No user engagement data available</p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        {/* Time Analysis Tab */}
        <TabsContent value="time-analysis" className="mt-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Messages by Day of Week Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Messages by Day of Week
                </CardTitle>
                <CardDescription>Activity patterns across weekdays</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Object.entries(metrics.messagesByDayOfWeek).map(([day, count]) => (
                    <div key={day} className="space-y-1">
                      <div className="flex justify-between">
                        <span className="text-sm font-medium">{day}</span>
                        <span className="text-sm text-[var(--app-main-text)]/70">{count}</span>
                      </div>
                      <Progress
                        value={maxDayMessages > 0 ? (count / maxDayMessages) * 100 : 0}
                        className="h-2"
                      />
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Messages by Hour Card */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Messages by Hour
                </CardTitle>
                <CardDescription>Activity distribution throughout the day</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="h-[300px] flex items-end justify-between">
                  {Object.entries(metrics.messagesByHour)
                    .filter((_, index) => index % 3 === 0) // Just show every 3 hours to fit
                    .map(([hour, count]) => {
                      const height = maxHourMessages > 0 ? (count / maxHourMessages) * 200 : 0;

                      return (
                        <div key={hour} className="flex flex-col items-center">
                          <div
                            className="w-8 bg-[var(--app-highlight)] rounded-t-md"
                            style={{ height: `${Math.max(height, 4)}px` }}
                          />
                          <div className="mt-2 text-xs text-[var(--app-main-text)]/70">
                            {hour}:00
                          </div>
                          <div className="text-sm font-medium">{count}</div>
                        </div>
                      );
                    })}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};