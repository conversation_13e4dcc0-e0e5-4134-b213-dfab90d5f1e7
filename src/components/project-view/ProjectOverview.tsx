import React from 'react';
import { Section, ChannelTopic, Message } from '@/lib/types';
import { useApp } from '@/lib/app-context';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { Calendar, MessageSquare, FileText, Users, ArrowRight, Hash, Sparkles } from 'lucide-react';
import { formatTimestamp } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';

interface ProjectOverviewProps {
  section: Section;
  topics: ChannelTopic[];
  recentMessages: Message[];
}

export const ProjectOverview: React.FC<ProjectOverviewProps> = ({
  section,
  topics,
  recentMessages
}) => {
  const { workspace, navigateToChannelTopic, showMemberProfile, setCurrentChannel } = useApp();

  // Helper function to find user by ID from current workspace
  const findUserById = (userId: string) => {
    return workspace?.users.find(u => u.id === userId);
  };

  // Sort topics by most recent activity
  const sortedTopics = [...topics].sort((a, b) =>
    new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  // Take top 3 most recent topics
  const recentTopics = sortedTopics.slice(0, 3);

  // Take top 5 most recent messages
  const topMessages = recentMessages.slice(0, 5);

  // Find unique users who have participated in this section
  const uniqueUserIds = new Set<string>();
  section.channels.forEach(channel => {
    channel.messages.forEach(message => {
      uniqueUserIds.add(message.userId);
    });
  });

  // Get today's message count
  const todayMessageCount = recentMessages.filter(msg => {
    const today = new Date();
    const msgDate = new Date(msg.timestamp);
    return msgDate.toDateString() === today.toDateString();
  }).length;

  // Generate an LLM-like summary (mock data)
  const projectSummary = `Project "${section.name}" is focused on ${section.channels.length} key areas, each with its own channel.
Recent discussions have centered around budget approvals, project kickoffs, and feature prioritization.
Key decisions were made about Q3 resource allocations and project timelines.
Team members have shared important files including the Q3 project plan and budget forecast.`;

  return (
    <div className="space-y-6 pb-8">
      <div className="space-y-2">
        <h2 className="text-2xl font-bold text-[var(--app-main-text)]">Project Overview</h2>
        <p className="text-[var(--app-main-text)]/70">
          A summary of key activities across {section.name}'s {section.channels.length} channels
        </p>
      </div>

      {/* Key Metrics Tiles at Top */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        {/* Channels Tile */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="rounded-full p-2 bg-blue-100 dark:bg-blue-900/30 mr-3">
                <Hash className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{section.channels.length}</div>
                <div className="text-sm text-[var(--app-main-text)]/70">Channels</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Topics Tile */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="rounded-full p-2 bg-purple-100 dark:bg-purple-900/30 mr-3">
                <MessageSquare className="h-5 w-5 text-purple-600 dark:text-purple-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{topics.length}</div>
                <div className="text-sm text-[var(--app-main-text)]/70">Topics</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Participants Tile */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="rounded-full p-2 bg-green-100 dark:bg-green-900/30 mr-3">
                <Users className="h-5 w-5 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{uniqueUserIds.size}</div>
                <div className="text-sm text-[var(--app-main-text)]/70">Participants</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Messages Today Tile */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center">
              <div className="rounded-full p-2 bg-orange-100 dark:bg-orange-900/30 mr-3">
                <MessageSquare className="h-5 w-5 text-orange-600 dark:text-orange-400" />
              </div>
              <div>
                <div className="text-2xl font-bold">{todayMessageCount}</div>
                <div className="text-sm text-[var(--app-main-text)]/70">Messages Today</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Main Content Area - Project Brief and Active Topics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {/* Project Summary Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5" />
              Project Brief
            </CardTitle>
            <CardDescription className="flex items-center">
              <Sparkles className="mr-1.5 h-3.5 w-3.5 text-purple-500" />
              AI-generated summary of recent project activities
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="text-base leading-relaxed">
              {projectSummary}
            </div>
          </CardContent>
        </Card>

        {/* Active Topics Card */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <MessageSquare className="mr-2 h-5 w-5" />
              Active Topics
            </CardTitle>
            <CardDescription>Most active discussions across channels</CardDescription>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[230px] pr-4">
              <div className="space-y-4">
                {recentTopics.map(topic => {
                  // Find the channel this topic belongs to
                  const channel = section.channels.find(ch =>
                    ch.channelTopics?.some(t => t.id === topic.id)
                  );

                  return (
                    <div key={topic.id} className="space-y-2">
                      <div className="flex items-start justify-between">
                        <div className="min-w-0 flex-1">
                          <div className="font-medium">
                            <span className="truncate mr-2">{topic.title}</span>
                          </div>
                          <div className="text-xs text-[var(--app-main-text)]/60 flex items-center mt-1">
                            <Hash className="h-3 w-3 mr-1 shrink-0" />
                            <button
                              onClick={() => channel && setCurrentChannel(channel.id)}
                              className="hover:underline"
                            >
                              {channel?.name || 'Unknown channel'}
                            </button>
                            <span className="mx-1">•</span>
                            {formatTimestamp(topic.createdAt)}
                            <Badge variant="outline" className="ml-2 text-xs px-1.5 py-0.5 h-4">
                              {topic.messageIds.length} msgs
                            </Badge>
                          </div>
                        </div>

                        {channel && (
                          <Button
                            variant="ghost"
                            size="sm"
                            className="h-7 text-xs shrink-0"
                            onClick={() => navigateToChannelTopic(channel.id, topic.id)}
                          >
                            View <ArrowRight className="h-3 w-3 ml-1" />
                          </Button>
                        )}
                      </div>
                      <p className="text-sm line-clamp-2">{topic.summary}</p>
                    </div>
                  );
                })}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            Recent Activity
          </CardTitle>
          <CardDescription>Latest messages across all channels</CardDescription>
        </CardHeader>
        <CardContent>
          <ScrollArea className="h-[280px] pr-4">
            <div className="space-y-4">
              {topMessages.map(message => {
                const user = findUserById(message.userId);
                // Find the channel this message belongs to
                const channel = section.channels.find(ch =>
                  ch.messages.some(m => m.id === message.id)
                );

                return (
                  <div key={message.id} className="flex items-start space-x-3">
                    <button
                      className="flex-shrink-0 h-8 w-8 rounded-full overflow-hidden bg-gray-200"
                      onClick={() => user && showMemberProfile(user.id)}
                    >
                      {user?.avatar && (
                        <img src={user.avatar} alt={user.name} className="h-full w-full object-cover" />
                      )}
                    </button>
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center text-sm flex-wrap">
                        <button
                          className="font-medium hover:underline"
                          onClick={() => user && showMemberProfile(user.id)}
                        >
                          {user?.name || 'Unknown user'}
                        </button>
                        <span className="mx-2 text-[var(--app-main-text)]/60">•</span>
                        <span className="text-xs text-[var(--app-main-text)]/60">{formatTimestamp(message.timestamp)}</span>
                        {channel && (
                          <>
                            <span className="mx-2 text-[var(--app-main-text)]/60">•</span>
                            <button
                              className="flex items-center text-xs text-[var(--app-main-text)]/60 hover:underline"
                              onClick={() => channel && setCurrentChannel(channel.id)}
                            >
                              <Hash className="h-3 w-3 mr-1" />
                              {channel.name}
                            </button>
                          </>
                        )}
                      </div>
                      <button
                        className="mt-1 text-sm line-clamp-2 text-left hover:underline decoration-dotted w-full"
                        onClick={() => channel && setCurrentChannel(channel.id)}
                      >
                        {message.content}
                      </button>
                    </div>
                  </div>
                );
              })}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>
    </div>
  );
};