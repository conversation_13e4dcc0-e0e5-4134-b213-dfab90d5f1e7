import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  Di<PERSON>Footer,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON><PERSON>le,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { useApp } from '@/lib/app-context';
import { toast } from 'sonner';

interface CreateWorkspaceDialogProps {
  children: React.ReactNode; // To allow triggering the dialog with a custom button or element
  onWorkspaceCreated?: (workspaceId: string) => void;
}

export function CreateWorkspaceDialog({ children, onWorkspaceCreated }: CreateWorkspaceDialogProps) {
  const { createWorkspace } = useApp();
  const [isOpen, setIsOpen] = useState(false);
  const [name, setName] = useState('');
  const [iconUrl, setIconUrl] = useState('');
  const [isCreating, setIsCreating] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!name.trim()) {
      toast.error('Workspace name cannot be empty.');
      return;
    }
    setIsCreating(true);
    try {
      const newWorkspace = await createWorkspace(name.trim(), iconUrl.trim() || undefined);
      if (newWorkspace) {
        toast.success(`Workspace "${newWorkspace.name}" created successfully!`);
        setName('');
        setIconUrl('');
        setIsOpen(false);
        if (onWorkspaceCreated) {
          onWorkspaceCreated(newWorkspace.id);
        }
      } else {
        // Error toast is handled within createWorkspace
      }
    } catch (error) {
      // Error toast is handled within createWorkspace or by handleUnexpectedError
      console.error('Failed to create workspace from dialog:', error);
    } finally {
      setIsCreating(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>{children}</DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Create New Workspace</DialogTitle>
          <DialogDescription>
            Enter the details for your new workspace. Click save when you're done.
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit}>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="workspace-name" className="text-right">
                Name
              </Label>
              <Input
                id="workspace-name"
                value={name}
                onChange={(e) => setName(e.target.value)}
                className="col-span-3"
                placeholder="e.g. Marketing Team"
                required
              />
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="workspace-icon-url" className="text-right">
                Icon URL
              </Label>
              <Input
                id="workspace-icon-url"
                value={iconUrl}
                onChange={(e) => setIconUrl(e.target.value)}
                className="col-span-3"
                placeholder="(Optional) e.g. https://example.com/icon.png"
              />
            </div>
          </div>
          <DialogFooter>
            <Button type="button" variant="outline" onClick={() => setIsOpen(false)} disabled={isCreating}>
              Cancel
            </Button>
            <Button type="submit" disabled={isCreating || !name.trim()}>
              {isCreating ? 'Creating...' : 'Create Workspace'}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}