import React, { useState } from 'react';
import { Lock, Users, Check } from 'lucide-react';
import { Button } from './ui/button';
import { toast } from 'sonner';

interface PrivateChannelMessageProps {
  channelName: string;
  onRequestAccess?: () => void;
}

export const PrivateChannelMessage: React.FC<PrivateChannelMessageProps> = ({
  channelName,
  onRequestAccess
}) => {
  const [requestSent, setRequestSent] = useState(false);

  const handleRequestAccess = () => {
    setRequestSent(true);
    toast.success(`Access request sent for #${channelName}`, {
      description: "A workspace admin will review your request and get back to you."
    });
    onRequestAccess?.();
  };
  return (
    <div className="flex flex-col items-center justify-center h-full p-8 text-center">
      <div className="w-16 h-16 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center mb-4">
        <Lock size={32} className="text-gray-400" />
      </div>

      <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-2">
        This is a private channel
      </h2>

      <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md">
        <span className="font-medium">#{channelName}</span> is a private channel.
        Only invited members can see and participate in this channel.
      </p>

      <div className="flex items-center gap-2 text-sm text-gray-500 dark:text-gray-400 mb-6">
        <Users size={16} />
        <span>Contact a channel member or workspace admin for access</span>
      </div>

      <Button
        onClick={handleRequestAccess}
        variant={requestSent ? "secondary" : "outline"}
        disabled={requestSent}
        className="flex items-center gap-2"
      >
        {requestSent ? (
          <>
            <Check size={16} />
            Request Sent
          </>
        ) : (
          <>
            <Lock size={16} />
            Request Access
          </>
        )}
      </Button>
    </div>
  );
};
