
import { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>nt, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { User, AtSign, MessageSquare, Edit3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { User as UserType } from '@/lib/types';
import { useApp } from '@/lib/app-context';
import { EditProfileDialog } from './EditProfileDialog';

interface MemberProfileDialogProps {
  member: UserType | null;
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const MemberProfileDialog = ({ member, open, onOpenChange }: MemberProfileDialogProps) => {
  const { addDirectMessage, getCurrentUser, workspace } = useApp();
  const [isEditProfileOpen, setIsEditProfileOpen] = useState(false);
  const currentUser = getCurrentUser();

  if (!member) return null;

  // Get the most up-to-date user data from workspace state
  // This ensures we show the latest information after profile updates
  const currentMemberData = workspace?.users.find(user => user.id === member.id) || member;

  const isOwnProfile = currentUser?.id === member.id;

  const handleDirectMessage = () => {
    addDirectMessage(member.id);
    onOpenChange(false);
  };

  const handleEditProfile = () => {
    setIsEditProfileOpen(true);
    onOpenChange(false); // Close the member profile dialog
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={currentMemberData.avatar} alt={currentMemberData.name} />
              <AvatarFallback>
                <User className="h-8 w-8" />
              </AvatarFallback>
            </Avatar>
            <div>
              <DialogTitle className="text-xl">{currentMemberData.name}</DialogTitle>
              <div className="flex items-center text-sm text-gray-500 mt-1">
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  currentMemberData.status === 'online' ? 'bg-green-500' :
                  currentMemberData.status === 'away' ? 'bg-yellow-500' : 'bg-gray-400'
                }`}></div>
                <span className="capitalize">{currentMemberData.status || 'offline'}</span>
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="pt-2 space-y-4">
          {currentMemberData.title && (
            <div className="flex items-center gap-2">
              <span className="text-gray-500">Title:</span>
              <span>{currentMemberData.title}</span>
            </div>
          )}

          <Separator />

          <div>
            <h3 className="font-medium mb-2">About</h3>
            <p className="text-sm text-gray-600">
              {currentMemberData.about || "No information available"}
            </p>
          </div>

          <Separator />

          <div className="flex justify-between">
            {isOwnProfile ? (
              <>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <AtSign size={14} />
                  Email
                </Button>
                <Button onClick={handleEditProfile} className="flex items-center gap-1">
                  <Edit3 size={14} />
                  Edit Profile
                </Button>
              </>
            ) : (
              <>
                <Button variant="outline" size="sm" className="flex items-center gap-1">
                  <AtSign size={14} />
                  Email
                </Button>
                <Button onClick={handleDirectMessage} className="flex items-center gap-1">
                  <MessageSquare size={14} />
                  Message
                </Button>
              </>
            )}
          </div>
        </div>
      </DialogContent>

      {/* Edit Profile Dialog - only render if this is the user's own profile */}
      {isOwnProfile && (
        <EditProfileDialog
          open={isEditProfileOpen}
          onOpenChange={setIsEditProfileOpen}
        />
      )}
    </Dialog>
  );
};
