import React from 'react';
import { getDefaultReactionEmojis } from '@/lib/emoji-data';
import { WorkspaceSettings } from '@/lib/types';
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface DefaultEmojiBarProps {
  onEmojiSelect: (emoji: string) => void;
  workspaceSettings?: WorkspaceSettings;
  className?: string;
}

export const DefaultEmojiBar: React.FC<DefaultEmojiBarProps> = ({
  onEmojiSelect,
  workspaceSettings,
  className = ""
}) => {
  const defaultEmojis = getDefaultReactionEmojis(workspaceSettings);

  const handleEmojiClick = (e: React.MouseEvent, emoji: string) => {
    e.stopPropagation();
    onEmojiSelect(emoji);
  };

  return (
    <div className={`flex items-center space-x-1 ${className}`}>
      <TooltipProvider delayDuration={100}>
        {defaultEmojis.map((emoji) => (
          <Tooltip key={emoji}>
            <TooltipTrigger asChild>
              <button
                onClick={(e) => handleEmojiClick(e, emoji)}
                className="flex items-center justify-center w-7 h-7 text-sm rounded hover:bg-[var(--app-hover-bg)] transition-colors duration-100 ease-in-out"
                aria-label={`React with ${emoji}`}
              >
                {emoji}
              </button>
            </TooltipTrigger>
            <TooltipContent side="top" className="text-xs p-1.5">
              React with {emoji}
            </TooltipContent>
          </Tooltip>
        ))}
      </TooltipProvider>
    </div>
  );
};
