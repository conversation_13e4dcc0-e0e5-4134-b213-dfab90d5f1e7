import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react"; // Added useEffect
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch"; // Added Switch
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ThemeSelectionGrid } from "./ui/ThemeSelectionGrid";
import { useApp } from "@/lib/app-context"; // Added useApp
import { WorkspaceSettings } from "@/lib/types"; // Added WorkspaceSettings
import { toast } from "sonner"; // Added for error feedback
import { DEFAULT_REACTION_EMOJIS } from "@/lib/emoji-data";
import { X } from "lucide-react";

interface WorkspaceSettingsDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export function WorkspaceSettingsDialog({
  open,
  onOpenChange,
}: WorkspaceSettingsDialogProps) {
  const { workspace, updateWorkspaceSettings } = useApp();
  const [currentSettings, setCurrentSettings] = useState<Partial<WorkspaceSettings>>(workspace?.settings || {});
  const [isSaving, setIsSaving] = useState(false);

  // Check if current user is admin
  const currentUser = workspace?.users.find(u => u.id === workspace?.currentUserId);
  const isAdmin = currentUser?.workspaceRole === 'admin';

  useEffect(() => {
    if (workspace?.settings) {
      setCurrentSettings(workspace.settings);
    }
  }, [workspace?.settings, open]); // Reset form when dialog opens or settings change

  // Close dialog if user is not admin
  useEffect(() => {
    if (open && !isAdmin) {
      toast.error('Access denied: Only workspace administrators can access workspace settings.');
      onOpenChange(false);
    }
  }, [open, isAdmin, onOpenChange]);

  const handleSettingChange = (key: keyof WorkspaceSettings, value: any) => {
    setCurrentSettings(prev => ({ ...prev, [key]: value }));
  };

  const handleSaveChanges = async () => {
    if (!workspace) return;

    // Double-check admin privileges before saving
    if (!isAdmin) {
      toast.error('Access denied: Only workspace administrators can modify workspace settings.');
      onOpenChange(false);
      return;
    }

    setIsSaving(true);
    try {
      await updateWorkspaceSettings(workspace.id, currentSettings);
      toast.success('Workspace settings saved successfully.');
      onOpenChange(false); // Close dialog on save
    } catch (error: any) {
      // Error handling is already done in updateWorkspaceSettings, but we can add additional handling here
      console.error('Error saving workspace settings:', error);
      // Don't show additional error toast since updateWorkspaceSettings already handles it
    } finally {
      setIsSaving(false);
    }
  };

  // Fallback for selectedTheme if not in currentSettings (e.g. initial load)
  const selectedTheme = currentSettings.theme || "default";

  const handleThemeChange = (theme: string) => {
    handleSettingChange('theme', theme);
    // Persistence is handled by updateWorkspaceSettings via handleSaveChanges
    console.log("Selected theme:", theme);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Workspace Settings</DialogTitle>
          <DialogDescription>
            Manage your workspace preferences and settings.
          </DialogDescription>
        </DialogHeader>
        <Tabs defaultValue="general" className="mt-4">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="appearance">Appearance</TabsTrigger>
            <TabsTrigger value="reactions">Reactions</TabsTrigger>
            <TabsTrigger value="notifications">Notifications</TabsTrigger>
          </TabsList>
          <TabsContent value="general" className="py-4">
            <div className="space-y-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="workspace-name" className="text-right">
                  Workspace Name
                </Label>
                <Input
                  id="workspace-name"
                  defaultValue="Acme Inc" // This should likely come from workspace.name if displayed
                  className="col-span-3"
                  // value={workspace?.name || "Acme Inc"} // Display actual name
                  // readOnly // Typically workspace name is not changed here, or via a different process
                />
              </div>
              {/* Removed the duplicate Workspace URL input block that was causing confusion */}
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="workspace-url-display" className="text-right">
                  Workspace URL
                </Label>
                <Input
                  id="workspace-url-display"
                  defaultValue="acme.threadflow.com" // This is likely display-only
                  className="col-span-3"
                  readOnly
                />
              </div>
              <div className="space-y-4 mt-4 pt-4 border-t">
                <div>
                  <Label htmlFor="initial-message-fetch-limit" className="text-sm font-medium">
                    Initial Message Fetch Limit
                  </Label>
                  <p className="text-sm text-muted-foreground mb-2">
                    Number of messages to load when opening a channel or DM (1-100, default: 25)
                  </p>
                  <Input
                    id="initial-message-fetch-limit"
                    type="number"
                    min="1"
                    max="100"
                    value={currentSettings.initialMessageFetchLimit ?? 25}
                    onChange={(e) => handleSettingChange('initialMessageFetchLimit', parseInt(e.target.value, 10))}
                    className="w-full"
                  />
                </div>
                <div>
                  <Label htmlFor="older-message-fetch-limit" className="text-sm font-medium">
                    Older Message Fetch Limit
                  </Label>
                  <p className="text-sm text-muted-foreground mb-2">
                    Number of older messages to load when scrolling up (1-100, default: 25)
                  </p>
                  <Input
                    id="older-message-fetch-limit"
                    type="number"
                    min="1"
                    max="100"
                    value={currentSettings.olderMessageFetchLimit ?? 25}
                    onChange={(e) => handleSettingChange('olderMessageFetchLimit', parseInt(e.target.value, 10))}
                    className="w-full"
                  />
                </div>
                <div>
                  <Label htmlFor="delta-message-fetch-limit" className="text-sm font-medium">
                    Delta Message Fetch Limit
                  </Label>
                  <p className="text-sm text-muted-foreground mb-2">
                    Number of newer messages to fetch during real-time sync (1-200, default: 50)
                  </p>
                  <Input
                    id="delta-message-fetch-limit"
                    type="number"
                    min="1"
                    max="200"
                    value={currentSettings.deltaMessageFetchLimit ?? 50}
                    onChange={(e) => handleSettingChange('deltaMessageFetchLimit', parseInt(e.target.value, 10))}
                    className="w-full"
                  />
                </div>
              </div>
              <div className="flex items-center justify-between mt-4 pt-4 border-t">
                <Label htmlFor="suppress-realtime-toast" className="flex flex-col space-y-1">
                  <span>Suppress Realtime Connection Toast</span>
                  <span className="font-normal leading-snug text-muted-foreground">
                    Hide the toast notification for temporary Realtime connection issues.
                  </span>
                </Label>
                <Switch
                  id="suppress-realtime-toast"
                  checked={currentSettings.suppressRealtimeConnectionToast ?? false}
                  onCheckedChange={(checked) => handleSettingChange('suppressRealtimeConnectionToast', checked)}
                />
              </div>
              <div className="flex items-center justify-between mt-4 pt-4 border-t">
                <Label htmlFor="hide-inaccessible-private-channels" className="flex flex-col space-y-1">
                  <span>Hide Inaccessible Private Channels</span>
                  <span className="font-normal leading-snug text-muted-foreground">
                    Hide private channels you cannot access from navigation (sidebar, quick nav). Sections will still be visible.
                  </span>
                </Label>
                <Switch
                  id="hide-inaccessible-private-channels"
                  checked={currentSettings.hideInaccessiblePrivateChannels ?? false}
                  onCheckedChange={(checked) => handleSettingChange('hideInaccessiblePrivateChannels', checked)}
                />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="appearance" className="py-4">
            <div className="space-y-4">
              <div>
                <Label>Theme</Label>
                <ThemeSelectionGrid
                  selectedTheme={selectedTheme}
                  onThemeChange={handleThemeChange}
                />
              </div>
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="font-size" className="text-right">
                  Font Size
                </Label>
                <Input
                  id="font-size"
                  type="number"
                  value={currentSettings.fontSize ?? 14}
                  onChange={(e) => handleSettingChange('fontSize', parseInt(e.target.value, 10))}
                  className="col-span-3"
                />
              </div>
            </div>
          </TabsContent>
          <TabsContent value="reactions" className="py-4">
            <div className="space-y-4">
              <div>
                <Label className="text-sm font-medium">Default Reaction Emojis</Label>
                <p className="text-sm text-muted-foreground mb-4">
                  Configure the default emoji set that appears for quick reactions on messages. Users can click these emojis to quickly react without opening the full emoji picker.
                </p>

                <div className="space-y-3">
                  <div className="flex flex-wrap gap-2 p-3 border rounded-lg bg-[var(--app-hover-bg)]">
                    {(currentSettings.defaultReactionEmojis || DEFAULT_REACTION_EMOJIS).map((emoji, index) => (
                      <div key={index} className="flex items-center bg-white dark:bg-gray-800 rounded px-2 py-1 border">
                        <span className="text-lg mr-2">{emoji}</span>
                        <button
                          onClick={() => {
                            const newEmojis = [...(currentSettings.defaultReactionEmojis || DEFAULT_REACTION_EMOJIS)];
                            newEmojis.splice(index, 1);
                            handleSettingChange('defaultReactionEmojis', newEmojis);
                          }}
                          className="text-gray-500 hover:text-red-500 transition-colors"
                          aria-label={`Remove ${emoji}`}
                        >
                          <X size={14} />
                        </button>
                      </div>
                    ))}
                  </div>

                  <div className="flex items-center space-x-2">
                    <Input
                      placeholder="Add emoji (e.g., 🚀)"
                      className="flex-1"
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          const input = e.target as HTMLInputElement;
                          const emoji = input.value.trim();
                          if (emoji && emoji.length <= 4) {
                            const currentEmojis = currentSettings.defaultReactionEmojis || DEFAULT_REACTION_EMOJIS;
                            if (!currentEmojis.includes(emoji)) {
                              handleSettingChange('defaultReactionEmojis', [...currentEmojis, emoji]);
                              input.value = '';
                            }
                          }
                        }
                      }}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        handleSettingChange('defaultReactionEmojis', DEFAULT_REACTION_EMOJIS);
                      }}
                    >
                      Reset to Default
                    </Button>
                  </div>

                  <p className="text-xs text-muted-foreground">
                    Press Enter to add an emoji. Maximum 12 emojis recommended for optimal display.
                  </p>
                </div>
              </div>
            </div>
          </TabsContent>
          <TabsContent value="notifications" className="py-4">
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch
                  id="email-notifications"
                  checked={currentSettings.emailNotifications ?? false}
                  onCheckedChange={(checked) => handleSettingChange('emailNotifications', checked)}
                />
                <Label htmlFor="email-notifications">
                  Email Notifications
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="desktop-notifications"
                  checked={currentSettings.desktopNotifications ?? false}
                  onCheckedChange={(checked) => handleSettingChange('desktopNotifications', checked)}
                />
                <Label htmlFor="desktop-notifications">
                  Desktop Notifications
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch
                  id="mobile-notifications"
                  checked={currentSettings.mobileNotifications ?? false}
                  onCheckedChange={(checked) => handleSettingChange('mobileNotifications', checked)}
                />
                <Label htmlFor="mobile-notifications">
                  Mobile Notifications
                </Label>
              </div>
            </div>
          </TabsContent>
        </Tabs>
        <DialogFooter>
          <Button onClick={handleSaveChanges} disabled={isSaving || !isAdmin}>
            {isSaving ? 'Saving...' : 'Save changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
