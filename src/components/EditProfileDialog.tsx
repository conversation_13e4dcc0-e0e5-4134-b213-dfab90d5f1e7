import React, { useState, useEffect, useRef } from 'react';
import { <PERSON><PERSON>, Dialog<PERSON>ontent, Di<PERSON>Header, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { User, Edit3, Save, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Avatar, AvatarImage, AvatarFallback } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';
import { User as UserType } from '@/lib/types';
import { useApp } from '@/lib/app-context';
import { toast } from 'sonner';

interface EditProfileDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const EditProfileDialog = ({ open, onOpenChange }: EditProfileDialogProps) => {
  const { getCurrentUser, updateUserProfile } = useApp();
  const currentUser = getCurrentUser();

  const [name, setName] = useState('');
  const [title, setTitle] = useState('');
  const [about, setAbout] = useState('');
  const [avatar, setAvatar] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Store initial user data to prevent form resets during optimistic updates
  const initialUserRef = useRef<typeof currentUser | null>(null);

  // Initialize form with current user data when dialog opens
  useEffect(() => {
    console.log('EditProfileDialog useEffect triggered. Open:', open, 'CurrentUser:', currentUser?.name);
    if (open && currentUser) {
      // Store the initial user data
      initialUserRef.current = currentUser;
      console.log('Setting form values to:', { name: currentUser.name, title: currentUser.title });
      setName(currentUser.name || '');
      setTitle(currentUser.title || '');
      setAbout(currentUser.about || '');
      setAvatar(currentUser.avatar || '');
    }
  }, [open]); // Only depend on 'open' to avoid resetting during typing

  const handleSave = async () => {
    if (!currentUser) return;

    setIsLoading(true);
    try {
      await updateUserProfile(currentUser.id, {
        name: name.trim(),
        title: title.trim() || undefined,
        about: about.trim() || undefined,
        avatar: avatar.trim() || undefined,
      });
      onOpenChange(false);
    } catch (error) {
      console.error('Error saving profile:', error);
      toast.error('Failed to save profile changes');
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    // Reset form to original values using the initial user data
    if (initialUserRef.current) {
      setName(initialUserRef.current.name || '');
      setTitle(initialUserRef.current.title || '');
      setAbout(initialUserRef.current.about || '');
      setAvatar(initialUserRef.current.avatar || '');
    }
    onOpenChange(false);
  };

  if (!currentUser) return null;

  return (
    <Dialog open={open} onOpenChange={(newOpen) => {
      console.log('Dialog onOpenChange called with:', newOpen);
      onOpenChange(newOpen);
    }}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <div className="flex items-center gap-4">
            <Avatar className="h-16 w-16">
              <AvatarImage src={avatar || currentUser.avatar} alt={name || currentUser.name} />
              <AvatarFallback>
                <User className="h-8 w-8" />
              </AvatarFallback>
            </Avatar>
            <div className="flex-1">
              <DialogTitle className="text-xl flex items-center gap-2">
                <Edit3 size={20} />
                Edit Profile
              </DialogTitle>
              <div className="flex items-center text-sm text-gray-500 mt-1">
                <div className={`w-2 h-2 rounded-full mr-2 ${
                  currentUser.status === 'online' ? 'bg-green-500' :
                  currentUser.status === 'away' ? 'bg-yellow-500' : 'bg-gray-400'
                }`}></div>
                <span className="capitalize">{currentUser.status || 'offline'}</span>
              </div>
            </div>
          </div>
        </DialogHeader>

        <div className="pt-2 space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Name *</Label>
            <Input
              id="name"
              value={name}
              onChange={(e) => {
                console.log('Name input changed:', e.target.value);
                setName(e.target.value);
              }}
              placeholder="Enter your name"
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="title">Title</Label>
            <Input
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              placeholder="e.g. Software Engineer, Designer"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="avatar">Avatar URL</Label>
            <Input
              id="avatar"
              value={avatar}
              onChange={(e) => setAvatar(e.target.value)}
              placeholder="https://example.com/avatar.jpg"
              type="url"
            />
          </div>

          <Separator />

          <div className="space-y-2">
            <Label htmlFor="about">About</Label>
            <Textarea
              id="about"
              value={about}
              onChange={(e) => setAbout(e.target.value)}
              placeholder="Tell others about yourself..."
              rows={3}
              className="resize-none"
            />
          </div>
        </div>

        <DialogFooter className="flex gap-2 pt-4">
          <Button
            variant="outline"
            onClick={handleCancel}
            disabled={isLoading}
            className="flex items-center gap-1"
          >
            <X size={14} />
            Cancel
          </Button>
          <Button
            onClick={handleSave}
            disabled={isLoading || !name.trim()}
            className="flex items-center gap-1"
          >
            <Save size={14} />
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
