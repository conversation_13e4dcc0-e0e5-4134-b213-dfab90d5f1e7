# Supabase Integration: High-Level Implementation Plan

This document provides high-level guidance for migrating the application's backend functionality to Supabase. It outlines the core architectural components, data structures, and key strategies.

## I. Core Objective

~~Transition from the current mock data provider (`src/lib/mock-data.ts`) to a fully functional backend powered by Supabase, supporting all existing client-side features including user management, workspaces, channels, messaging, real-time updates, and user preferences.~~

**Progress (2025-01-21):** The application has been fully switched from mock data to Supabase backend. All mock data fallbacks have been removed, and the application now exclusively uses real backend data. This ensures clear identification of missing backend features and maintains clean architecture without mixing mock and real data.

## II. Supabase Project Foundation

*   **Project Initialization:** A new Supabase project will be created.
*   **Authentication:** Supabase Auth will be the primary mechanism for user authentication (sign-up, login, session management). Email/Password will be the initial method.
*   **API Access:** The Supabase JavaScript client library will be used for all client-server interactions.

## III. High-Level Database Schema Overview

The database will be structured to mirror and expand upon the entities defined in `src/lib/types.ts`. UUIDs will generally be used for primary identifiers.

1.  **`Profiles` (Users)**
    *   **Links to:** Supabase `auth.users` (one-to-one).
    *   **Key Attributes:** `id` (matches `auth.users.id`), `name`, `avatar_url`, `status`, `status_message`, `title`, `classification`, `about`, `settings` (JSONB for `UserSettings`).
    *   **Purpose:** Stores application-specific user information beyond basic auth.

2.  **`Workspaces`**
    *   **Key Attributes:** `id`, `name`, `icon_url`, `settings` (JSONB for `WorkspaceSettings`), `owner_id` (references a Profile).
    *   **Purpose:** Represents distinct collaborative environments.
    *   **Settings Implementation:** The `settings` JSONB field includes workspace-level configurations such as message fetch limits, default reaction emojis, and privacy display preferences (see WorkspaceSettings section below).

3.  **`Workspace_Users` (Membership Junction Table)**
    *   **Links:** `Workspaces` and `Profiles` (many-to-many).
    *   **Key Attributes:** `workspace_id`, `user_id`, `role` (`admin`, `member`).
    *   **Purpose:** Manages user membership and roles within workspaces.

4.  **`Sections`**
    *   **Links to:** `Workspaces` (one-to-many).
    *   **Key Attributes:** `id`, `workspace_id`, `name`, `order` (for display sequence).
    *   **Purpose:** Organizes channels within a workspace.

5.  **`Channels`**
    *   **Links to:** `Workspaces`, `Sections`.
    *   **Key Attributes:** `id`, `workspace_id`, `section_id` (optional), `name`, `description`, `is_private`, `created_at`, `channel_note`, `settings` (JSONB for channel-specific views/defaults), `last_message_timestamp`.
    *   **Purpose:** Core communication spaces within workspaces.
    *   **Privacy Implementation:** The `is_private` field controls channel visibility and access. Private channels require explicit membership for access, while public channels are accessible to all workspace members.

6.  **`Channel_Members` (Membership Junction Table)**
    *   **Links:** `Channels` and `Profiles` (many-to-many).
    *   **Key Attributes:** `channel_id`, `user_id`.
    *   **Purpose:** Manages user membership in channels.

7.  **`Channel_Topics`**
    *   **Links to:** `Channels` (one-to-many).
    *   **Key Attributes:** `id`, `channel_id`, `title`, `summary`, `created_at`, `creator_id`, `is_archived` (BOOLEAN, default FALSE), `archived_at` (TIMESTAMPTZ, nullable), `archived_by` (UUID, references `profiles.id`, nullable).
    *   **Purpose:** Organizes discussions within a channel around specific topics. Supports archiving.

8.  **`Messages`**
    *   **Links to:** `Channels` (for channel messages), `Direct_Message_Sessions` (for DM messages), `Profiles` (for sender), `Messages` (for threads, self-referential), `Channel_Topics`.
    *   **Key Attributes:** `id`, `channel_id` (nullable), `dm_id` (nullable), `user_id` (sender), `content`, `timestamp`, `edited_at`, `parent_message_id` (for threads, nullable), `topic_id` (nullable, for channel topics), `also_send_to_channel` (for thread replies), `is_archived` (BOOLEAN, default FALSE), `archived_at` (TIMESTAMPTZ, nullable), `archived_by` (UUID, references `profiles.id`, nullable).
    *   **Constraint:** Must belong to either a channel or a DM.
    *   **Purpose:** Stores all communication records. Supports archiving (can be independent or coupled with topic archiving).

9.  **`Reactions`**
    *   **Links to:** `Messages`, `Profiles`.
    *   **Key Attributes:** `id`, `message_id`, `user_id`, `emoji`.
    *   **Purpose:** Manages user reactions to messages.

10. **`Direct_Message_Sessions` (DM Conversations)**
    *   **Key Attributes:** `id`, `name` (for group DMs), `created_at`, `last_message_timestamp`.
    *   **Purpose:** Represents a direct messaging conversation.

11. **`Direct_Message_Participants` (Membership Junction Table)**
    *   **Links:** `Direct_Message_Sessions` and `Profiles` (many-to-many).
    *   **Key Attributes:** `dm_id`, `user_id`.
    *   **Purpose:** Manages participants in a DM session.

12. **`Files` (Metadata - Storage Implementation Deferred)**
    *   **Links to:** `Profiles` (uploader), `Channels` (if uploaded to a channel), `Messages` (if attached to a message).
    *   **Key Attributes:** `id`, `name`, `type` (MIME), `url` (see "Storage Strategy" below), `size_bytes`, `uploaded_by_user_id`, `created_at`, `channel_id` (nullable), `message_id` (nullable), `is_pinned_in_channel_id` (nullable).
    *   **Purpose:** Stores metadata about uploaded files, supporting the enhanced message attachment UX.
    *   **Storage Strategy for `url` field (Phased Approach):**
        *   **Initial Phase (Message Input UX v1 - Base64/Text Content):** For rapid implementation of the enhanced message input UX, the `url` field may temporarily store base64 encoded data for pasted/dropped images, or extracted text content for pasted/dropped plain text files. This approach facilitates immediate UI goals but is not optimal for database size or managing diverse file types. The actual content (base64 string or text) would be stored in this `url` field.
        *   **Definitive Phase (Full Supabase Storage Integration):** The `url` field will store the canonical Supabase Storage object URL for all file types. This is the long-term, recommended approach for scalability, performance, and proper file management.
    *   **Note:** The client-side `Message` type includes a `files?: File[]` array to hold structured file information associated with a message.

13. **`User_App_State` (Optional Persistent Client State)**
    *   **Links to:** `Profiles`, `Workspaces`, `Channels`, `Direct_Message_Sessions`, `Messages` (for active thread).
    *   **Key Attributes:** `user_id`, `current_workspace_id`, `current_channel_id`, `current_dm_id`, `active_thread_id`.
    *   **Purpose:** Persists UI state like current selections across sessions/devices if deemed necessary. Often managed client-side.

## IV. Private vs. Public Channel UX Implementation (Completed 2025-01-21)

### A. Channel Privacy Model
*   **Public Channels**: Accessible to all workspace members for reading. Members can join/leave to control notifications and active participation.
*   **Private Channels**: Only invited members can see, access, and participate. Invitation = automatic membership.
*   **Visual Indicators**: Lock (🔒) icons for private channels, Hash (#) icons for public channels throughout the UI.

### B. Frontend Implementation (Client-Side Filtering)
*   **Channel Creation**: Enhanced "Add Channel" dialog with privacy toggle, clear descriptions, and dynamic button text.
*   **Access Control**: Private channel access denial shows user-friendly message with "Request Access" placeholder functionality.
*   **Visual Distinction**: Consistent Lock/Hash iconography in sidebar, headers, and command dialog.

### C. Workspace Setting: Hide Inaccessible Private Channels
*   **Setting**: `hideInaccessiblePrivateChannels` (boolean, default: false) in WorkspaceSettings JSONB.
*   **Scope**: When enabled, filters out private channels user cannot access from:
    *   Sidebar channel lists
    *   Ctrl+K quick navigation (CommandDialog)
*   **Implementation**: Client-side filtering in both Sidebar.tsx and CommandDialog.tsx components.
*   **Security Note**: This is client-side filtering for UX purposes. Channels remain in the payload but are hidden from navigation. Users cannot access channel content due to RLS policies, so security risk is minimal while providing UX flexibility. Future enhancement could implement backend/RLS-level filtering for stronger privacy.
*   **Section Preservation**: Sections remain visible even when all their channels are hidden.
*   **Admin Control**: Only workspace admins can modify this setting via WorkspaceSettingsDialog.

### D. Request Access Flow
*   **Placeholder Implementation**: "Request Access" button shows state change ("Request Sent") with toast notification.
*   **Future Enhancement**: Can be extended to actual invitation/request workflows when member management is fully implemented.

### E. Technical Details
*   **Components Updated**: Sidebar.tsx, MainContent.tsx, CommandDialog.tsx, WorkspaceSettingsDialog.tsx, PrivateChannelMessage.tsx (new).
*   **Backend Integration**: Updated addChannel function to accept isPrivate parameter.
*   **Type Safety**: Enhanced WorkspaceSettings interface with new privacy setting.
*   **Real-time Updates**: Setting changes apply immediately without page refresh.

## V. Authentication and Authorization Strategy

*   **Authentication:**
    *   Client-side will use `supabase.auth.signUp()`, `signInWithPassword()`, `signOut()`.
    *   The Supabase JS SDK will manage user sessions and JWTs.
    *   A new entry in the `Profiles` table will be automatically created (e.g., via a database trigger) when a new user signs up in `auth.users`.
*   **Authorization (Row Level Security - RLS):**
    *   RLS policies will be the primary mechanism for controlling data access.
    *   **General Principle:** Users can only access/modify data they own or are explicitly granted permission to through their memberships (workspaces, channels, DMs).
    *   **Private Channel Security:** RLS policies enforce that users can only access private channels they are members of, while the client-side filtering (Section IV.C) provides additional UX improvements.
    *   Examples:
        *   A user can only see workspaces they are a member of.
        *   A user can only see channels within their accessible workspaces (public channels or private channels they are a member of).
        *   A user can only send messages to channels/DMs they are part of.
        *   Users can only edit/delete their own messages (potentially with time limits).
*   **RLS Policy Review (May 2025 - Completed):** A thorough review of RLS policies was conducted. Key corrections and security enhancements were implemented for tables including `channels` (creation permissions), `direct_message_participants` (secure inserts), `direct_message_sessions` (secure inserts), and `files` (secure select for unassociated files). These changes are reflected in the respective `supabase/rls_*.sql` files.

## V. Real-Time Functionality Strategy

*   **Supabase Realtime:** Leveraged for live updates.
*   **Key Real-time Features:**
    1.  **New Messages:** Clients subscribe to new message insertions in their currently active channel or DM. The subscription payload should ideally contain the full new message data to avoid an extra fetch.
    2.  **Message Edits/Deletions:** Subscriptions for updates/deletions on messages.
    3.  **Reactions:** Live updates for new/removed reactions.
    4.  **User Presence:**
        *   Clients update their status in their `Profiles` record.
        *   Other clients subscribe to status changes on `Profiles` records of relevant users (e.g., members of the current workspace/channel). Supabase's built-in presence capabilities for Realtime channels can also be utilized.
    5.  **Typing Indicators:** Use Supabase Realtime's ephemeral message capabilities on specific channels (e.g., `typing:channel_id`).
*   **Client-Side Handling:** The client will listen to these real-time events and update its local state and UI accordingly.

## VI. Data Synchronization and API Logic (Client-Side)

*   **Data Fetching (Initial & Catch-up):**
    *   On app load/login, fetch essential initial data: user profile, workspaces, DMs.
    *   When navigating to a channel/DM:
        *   **Delta Sync:** Maintain a `last_fetched_message_timestamp` locally per channel/DM. Fetch messages newer than this timestamp.
        *   **Initial Load/Backfill:** If no local timestamp or for older history, fetch the latest `N` messages, implementing pagination/infinite scroll for older messages.
*   **Data Mutation (Create, Update, Delete):**
    *   Use standard Supabase JS client methods (`.insert()`, `.update()`, `.delete()`) for all data modifications.
    *   These operations will be subject to RLS policies.
*   **PostgreSQL Functions (for complex/atomic operations):**
    *   Database functions (RPCs) are used for operations requiring atomicity, complex logic, or centralized business rules. Examples include:
        *   `send_message`: Handles permission checks, message insertion, and creation/linking of associated file metadata in the `public.files` table. It will be enhanced to accept attachment data (initially as JSON objects representing local attachments, later as an array of pre-uploaded file IDs).
        *   `create_direct_message_session`: Creates DM sessions and adds participants.
        *   (New) Channel Topic Management: RPCs will handle CRUD (Create, Read, Update, Delete) and Archive/Unarchive operations for `channel_topics`, including logic for coupled message archiving.
*   **RPC Function Review (May 2025 - Completed):** A review of critical RPC functions (database functions) was completed. Corrections were made to functions like `send_message` (parameter handling), `create_direct_message_session` (bug fix for detecting existing sessions), channel topic management RPCs (security model adjusted to `SECURITY INVOKER` where appropriate), and helper functions (`is_dm_participant`, `is_channel_member` - `search_path` added). These changes are reflected in [`supabase/rpc_functions.sql`](supabase/rpc_functions.sql).

## VII. Data Seeding (from `mock-data.ts`)

*   **Purpose:** Populate the development database with initial data for testing and development.
*   **Strategy:**
    1.  Develop a script (e.g., Node.js) that uses the Supabase Admin SDK (with a service role key).
    2.  The script will:
        *   Create mock users in `auth.users` via `supabase.auth.admin.createUser()`.
        *   Transform data from `mock-data.ts` to match the new database schemas, mapping mock user IDs to the newly created Supabase user IDs.
        *   Insert the transformed data into the respective Supabase tables in the correct order to respect foreign key constraints.

## VIII. Client-Side Data Provider Replacement

~~**New Data Layer:** Create a dedicated module (e.g., `src/lib/supabase-data-provider.ts` or similar).~~
~~**Functions:** This module will encapsulate all Supabase client calls for fetching and mutating data.~~
~~**Gradual Refactoring:** Systematically replace all usages of `src/lib/mock-data.ts` in UI components and hooks with calls to the new Supabase data provider functions.~~
~~**State Management:** Ensure client-side state management (e.g., Zustand, Redux, Context) correctly handles loading states, errors, and reflects data fetched from Supabase, including real-time updates.~~

**COMPLETED (2025-01-21):**
*   **Data Layer:** `src/lib/supabase-data-provider.ts` has been implemented and is fully functional.
*   **Mock Data Removal:** All usages of `src/lib/mock-data.ts` have been systematically removed from UI components and hooks. The mock data file has been renamed to `src/lib/mock-data.DEPRECATED.ts` to prevent imports.
*   **Utility Functions:** Created `formatTimestamp` utility in `src/lib/utils.ts` and local `findUserById` helper functions in components that need to find users from the current workspace.
*   **State Management:** The app context correctly handles loading states, errors, and reflects data fetched from Supabase, including real-time updates.
*   **Clean Architecture:** The application now exclusively uses real backend data, making it clear what functionality still needs backend implementation.

## IX. E2E CLI Testing Strategy (Conceptual)

*   **Tooling:** A separate command-line interface (CLI) tool, likely built with Node.js and the Supabase JS client.
*   **Purpose:** To automate testing of core user flows and data integrity from an end-to-end perspective.
*   **Key Scenarios to Test:**
    1.  User sign-up and profile creation.
    2.  Workspace creation and user joining.
    3.  Channel creation and user joining (public/private).
    4.  Sending/receiving messages in channels (including threads).
    5.  Sending/receiving direct messages.
    6.  User status updates and visibility to other users.
    7.  Basic RLS enforcement (e.g., user cannot see data from a workspace they are not part of).
*   **Methodology:** The CLI will simulate actions of different test users and verify expected outcomes by querying the database (respecting user context for RLS checks) or observing real-time events.

## X. Future Considerations (Post-Initial Full Migration)

*   **Advanced Search:** Evaluate the need for server-side full-text search capabilities if client-side search becomes insufficient.
*   **Edge Functions:** Implement Supabase Edge Functions for:
    *   Complex backend logic not suitable for client-side or simple database functions.
    *   Notifications.
    *   Third-party API integrations.
    *   **LLM Processing for Topics:** Asynchronous tasks like summarizing channel topics or auto-tagging messages, potentially triggered by database changes (via webhooks to Edge Functions) or from RPCs inserting jobs into a processing queue.
    *   **File Storage & Attachments:** Implement the enhanced file/image attachment UX.
        *   **Initial Phase:** Support pasting/dropping images (as base64) and text file content, storing this data temporarily in `public.files.url`. This involves client-side logic for handling these inputs and backend updates to `send_message` RPC to process this data.
        *   **Definitive Phase:** Fully integrate Supabase Storage for handling uploads of all supported file types. Update `public.files.url` to store Supabase Storage object URLs. This involves client-side upload mechanisms (to Supabase Storage directly) and updating the `send_message` RPC to link these pre-uploaded files.
    *   **Scalability & Performance Tuning:** As the application grows, monitor performance and implement optimizations such as advanced indexing, query optimization, or exploring Supabase's scaling options.
*   **Enhanced Offline Support:** If deeper offline capabilities are required, investigate more sophisticated client-side caching and synchronization strategies.

This high-level plan provides the roadmap for the Supabase integration. The next phase involves the detailed design and implementation of each component.

## XI. Env

We have added the following to .env already you can leverage for implementation and testing:

VITE_SUPABASE_URL="https://ryqzoxfsvdqokegjmbwj.supabase.co"
VITE_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InJ5cXpveGZzdmRxb2tlZ2ptYndqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4NDUwMDUsImV4cCI6MjA2MzQyMTAwNX0.HIUYOgdJG0rztzKuZ3_uiH8dlRC87VLNULQJWkbu_Hk"

VITE_TEST_USER_EMAIL="<EMAIL>"
VITE_TEST_USER_PASSWORD="password"

## XII. Implementation Progress & Next Steps (as of 2025-05-21)

This section documents the progress made during the initial Supabase integration session and outlines the immediate path forward.

### A. Supabase Project & Client Setup:
*   **Project Confirmed**: The Supabase project (`id: ryqzoxfsvdqokegjmbwj`, URL: `https://ryqzoxfsvdqokegjmbwj.supabase.co`) is active. API URL and Anon Key are configured via `.env` variables (`VITE_SUPABASE_URL`, `VITE_SUPABASE_ANON_KEY`).
*   **JS Client Initialized**: The Supabase JavaScript client (`@supabase/supabase-js`) has been installed and is initialized in `src/lib/supabaseClient.ts`.
*   **Database Schema**: Initial exploration confirms that tables like `profiles`, `workspaces`, `channels`, `messages`, etc., exist, generally aligning with the schema outlined in Section III. Primary keys are UUIDs.
*   **Row Level Security (RLS)**: Extensive RLS policies are already active on the database tables.
*   **Edge Functions**: No custom edge functions were observed initially.

### B. Authentication & Authorization:
*   **Client-Side Auth UI**: `src/pages/Auth.tsx` created to handle email/password sign-up and login using the Supabase JS SDK.
*   **Routing**: `/auth` route added to `src/App.tsx`. Unauthenticated users accessing the root path (`/`) are redirected to `/auth`.
*   **Auth Context**: `src/lib/auth-context.tsx` created. `AuthProvider` manages user sessions, authentication state, and fetches the user's profile from `public.profiles`. It uses `supabase.auth.onAuthStateChange` for live updates.
*   **Profile Creation Trigger**: A PostgreSQL function (`public.handle_new_user`) and trigger (`on_auth_user_created`) were added to the Supabase database. This automatically creates a new entry in `public.profiles` when a new user signs up in `auth.users`, populating `name` and `avatar_url` from `raw_user_meta_data`.
*   **Test User Credentials**: `AuthPage.tsx` now pre-fills login details from `VITE_TEST_USER_EMAIL` and `VITE_TEST_USER_PASSWORD` if available.
*   **Auto-Login for Testing (Enhanced 2025-05-22)**: `AuthPage.tsx` was enhanced to automatically submit the login form. In development mode (`import.meta.env.DEV`), it prioritizes `test_email` and `test_password` URL query parameters (e.g., `/auth?test_email=<EMAIL>&test_password=pw`). If URL parameters are not provided, it falls back to using `VITE_TEST_USER_EMAIL` and `VITE_TEST_USER_PASSWORD` from the `.env` file. This allows flexible testing with different users by modifying the URL, bypassing manual form interaction. (Note: When constructing URLs with multiple query parameters for browser automation tools, ensure parameters are separated by a literal `&` and not an HTML entity like `&`, as the latter can cause parsing issues for URL parameter readers like `URLSearchParams`.)

### C. Data Seeding:
*   **Strategy**:
    *   Initial seeding focused on one existing test user (`id: a8fd5e82-c354-4e3b-b765-f2f1f4591dad`, "John Smith") using `execute_sql` MCP tool for basic data.
    *   A Node.js script (`seed_test_users.js`) was developed to programmatically create additional test users (Jane Doe, Alex Johnson, Sam Wilson) using the Supabase Admin SDK (via `service_role_key`). This script handles auth user creation, profile updates, workspace/channel memberships, and DM session creation, referencing `src/lib/mock-data.ts` for user details.
*   **Data Seeded for John Smith (via `execute_sql` MCP tool)**:
    *   John Smith's profile in `public.profiles` updated with mock data details.
    *   "Acme Inc." workspace created (UUID: `10000000-0000-0000-0000-000000000001`), with John Smith as admin.
    *   Sections ("Team Workspace", "Project Alpha (Dev)", "Project Beta (Design)") created within Acme Inc. using predefined UUIDs.
    *   Channels ("general", "random", "dev-team", "design-ux") created within respective sections using predefined UUIDs.
    *   John Smith added as a member to these four channels.
    *   Channel topics created by John Smith in these channels.
    *   Messages sent by John Smith added to these channels/topics.
    *   Metadata for two files uploaded by John Smith added and associated with the "general" channel.
    *   `active_channel_topic_id` updated for "general" and "random" channels.

### D. Client-Side Data Handling:
*   **`supabase-data-provider.ts`**:
    *   Created in `src/lib/`.
    *   `getUserProfile(userId)`: Implemented to fetch a user's profile.
    *   `getInitialWorkspaceDataForUser(userId)`: Partially implemented. It fetches the user's first workspace, its basic details, members (profiles), sections, and channels (with member IDs, but empty message/topic/file arrays for now).
    *   `getMessagesForChannel(channelId)`: Implemented to fetch top-level messages for a channel, including basic sender profile info.
*   **`app-context.tsx` (`AppProvider`)**:
    *   Integrates `AuthContext` to get the authenticated user.
    *   On login, calls `getInitialWorkspaceDataForUser` to load data. If this returns `null` (e.g., new user, error), it falls back to a modified version of `mockWorkspaces['w1a2b3c4d']` where `currentUserId` is the actual logged-in user.
    *   A `useEffect` hook has been added to call `getMessagesForChannel` when the `currentChannelId` (from `workspace.currentChannelId`) changes, updating the channel's messages in the local state.
    *   The `sendMessage` function now:
        *   Constructs a message object for Supabase insertion.
        *   Performs an optimistic update to the local `workspace` state.
        *   Calls `supabase.rpc('send_message', ...)` to save the message to the database (this RPC handles message insertion and attachment linking).
*   **Type Definitions (`src/lib/types.ts`)**:
    *   `Workspace` interface updated to include `owner_id?: string;`.
    *   `Message` interface includes `files?: File[]` to hold structured metadata for attachments.
    *   **Enhanced Section and Channel Discoverability (May 2025 - Technical Implementation Completed):**
        *   To align with a Slack-like UX where all sections and channels are discoverable by workspace members (with content access gated by permissions), technical changes were implemented:
            *   **Backend RLS:** The `SELECT` RLS policy on the `public.channels` table was updated to allow all workspace members to discover all channel entries in their workspace. This change is reflected in [`supabase/rls_channels.sql`](supabase/rls_channels.sql).
            *   **Client-Side Data Fetching:** The query in `getInitialWorkspaceDataForUser` ([`src/lib/supabase-data-provider.ts`](src/lib/supabase-data-provider.ts:267)) was modified to fetch all sections and their associated channels (using `channels(*, channel_members(user_id))`) instead of an `INNER JOIN`-like behavior.
        *   **Status:** Core backend and client data-fetching changes are complete.
        *   **Next Steps (Tracked in Section XIII - Roadmap):**
            *   Full UI/UX verification for all access scenarios (public, private-member, private-non-member).
            *   Specific E2E tests for these visibility and access rules need to be created/updated.

### E. RLS Troubleshooting and Resolution (Session of 2025-05-21):

*   **Initial Problem**: Persistent PostgreSQL errors (`"missing FROM-clause entry for table 'is_workspace_member'"` and later `"infinite recursion detected in policy for relation 'workspace_users'"`) prevented `getInitialWorkspaceDataForUser` and `getMessagesForChannel` from loading data from Supabase, causing the app to fall back to mock data.
*   **Root Cause Analysis**:
    *   The `"missing FROM-clause..."` error was traced to RLS policies (particularly on `sections` and `messages` when involved in complex queries with joins) that directly used the SQL function `public.is_workspace_member(column_name)`. It appears PostgreSQL's query planner struggled with this function in RLS policy contexts for these queries.
    *   The `"infinite recursion..."` error was traced to the RLS policy on `workspace_users` when it attempted to define "user can see all members of their workspaces" using an `EXISTS` subquery that referenced `workspace_users` itself. This, possibly in combination with other RLS policies that also queried `workspace_users` (e.g., the `workspaces` policy), led to a recursive loop.
*   **Solution Implemented**:
    1.  **Inlining RLS Logic for SELECT Policies**: For tables like `workspaces`, `sections`, `channels` (SELECT policy), `channel_members`, and `messages` (SELECT policy), RLS policies that previously might have used helper SQL functions (like `public.is_workspace_member()`) were modified to inline the equivalent logic. This typically involves an `EXISTS (SELECT 1 FROM public.workspace_users wu WHERE wu.workspace_id = relevant_table.workspace_id_column AND wu.user_id = auth.uid())` subquery. This resolved the "missing FROM-clause" errors during data fetching.
    2.  **Simplifying `workspace_users` SELECT RLS**: The SELECT RLS policy on `public.workspace_users` ("Users can view workspace members") was simplified to `USING (user_id = auth.uid())`. This resolved the "infinite recursion" error.
    3.  **Inlining RLS Logic for INSERT Policies**:
        *   The INSERT RLS policy on `public.channels` ("Workspace members can create channels"), which likely used `public.is_workspace_member()` in its `WITH CHECK` clause, was modified to use an inlined `EXISTS(...)` subquery checking workspace membership. This resolved the "missing FROM-clause" error during channel creation.
        *   The INSERT RLS policy on `public.messages` ("Users can send messages to channels and DMs they have access to"), which likely used a helper function like `is_channel_member()` (that itself might have called `is_workspace_member()`) in its `WITH CHECK` clause, was modified. The problematic function call was replaced with inlined `EXISTS(...)` subqueries for checking both workspace membership (via the channel) and direct channel membership. This resolved the "missing FROM-clause" error during message sending.
    4.  **`profiles` SELECT RLS**: The SELECT RLS policy on `public.profiles` ("Users can view all profiles") was set to `USING (auth.role() = 'authenticated')`.
    5.  **`direct_message_sessions` SELECT RLS**: The SELECT RLS policy ("Users can view DM sessions they participate in"), which used `is_dm_participant()`, was updated to use inlined `EXISTS(...)` logic checking `direct_message_participants`. This resolved visibility issues for DMs at the database level.
    6.  **`handle_new_user` Trigger**: Corrected the trigger to use `NEW.raw_user_meta_data->>'name'` (instead of `->>'full_name'`) to ensure correct profile name seeding. Existing test user profiles were manually updated.
    7.  **DM Data Fetching (Client)**: `getInitialWorkspaceDataForUser` in `supabase-data-provider.ts` now correctly fetches DM sessions and consolidates all DM participant profiles into the main `workspace.users` list. `app-context.tsx` correctly stores this data. `Sidebar.tsx` now lists DMs with correct participant names.
    8.  **DM Message Fetching (Client)**: `getMessagesForDirectMessage` function added to `supabase-data-provider.ts`, and `app-context.tsx` now calls it when a DM is selected to load messages for that DM.
*   **Outcome**: RLS policies for SELECT operations on core tables are stable. Test users are seeded correctly with proper names. DM sessions are listed correctly in the UI. Channel message sending and creation work.
*   **Key Learning**:
    *   Direct use of SQL helper functions within RLS policies is highly problematic. Inlining logic is more robust.
    *   `SECURITY DEFINER` functions with `SET search_path = ''` and fully qualified names are necessary if helper functions are used in RLS or complex backend logic.
    *   Persistent errors like "missing FROM-clause entry for table new" when defining `INSERT` RLS policies can indicate deep issues, potentially requiring workarounds like RPC calls for inserts.
    *   **Current Blocker**: Sending messages in Direct Messages (DMs) fails with a 403 Forbidden error. This is due to the `INSERT` RLS policy on `public.messages` not correctly authorizing inserts for DMs. Attempts to fix this policy (even with helper functions or minimal definitions using `new.column_name`) have consistently failed with a "missing FROM-clause entry for table new" error when applied via the MCP tool. Even when the user applied a corrected policy directly in the Supabase SQL editor, the 403 error on DM insert persists in the app.
*   **Resolution for Message Sending (DM and Channel)**:
    *   The persistent 403 errors and "missing FROM-clause" issues with the `INSERT` RLS policy on `public.messages` (especially for DMs) necessitated a shift in strategy.
    *   **Solution**: A `SECURITY DEFINER` PostgreSQL database function (RPC) named `public.send_message(p_content TEXT, p_workspace_id UUID, p_channel_id UUID, p_dm_id UUID, p_parent_message_id UUID, p_topic_id UUID)` was implemented.
        *   This function is a **database stored function**, not a Supabase Edge Function.
        *   It internally performs permission checks by calling the existing helper functions (`public.is_allowed_to_send_to_channel` and `public.is_allowed_to_send_to_dm`).
        *   If authorized, it performs the `INSERT` into `public.messages`. The `workspace_id` is not directly inserted into `messages` as it's derived via channel/DM relationships; the `p_workspace_id` parameter is for potential use in helper functions.
    *   **Client-Side Update**: The `sendMessage` function in `src/lib/app-context.tsx` was refactored to call `supabase.rpc('send_message', ...)` instead of `supabase.from('messages').insert(...)`.
    *   **RLS Simplification**: The `INSERT` RLS policy on `public.messages` was simplified to `WITH CHECK (user_id = auth.uid())`, as the primary authorization logic is now handled within the RPC.
    *   **Outcome**: This approach successfully resolved the message sending issues for both DMs and channels.
    *   **Key Takeaway**: For complex write authorizations (like message sending involving checks on related tables), using a `SECURITY DEFINER` database RPC function is a more robust and reliable pattern than attempting to implement all logic within RLS `INSERT` or `UPDATE` policies, especially when encountering persistent RLS evaluation issues.

### F. Current Status & Next Steps (as of 2025-05-21, with updates from 2025-05-22 planning):

Core data fetching, RLS policies for SELECT, and message sending via RPC are stable. Test users are seeded. DMs are listed and messages load. UI behavior on tab focus has been improved.

The immediate next steps focus on enhancing the chat experience, particularly around unread message handling and real-time updates.

**1. Implement Unread Badge Persistence (High Priority for UX)**
    *   **Problem**: Unread message badges are lost on app reload because their state is not persisted.
    *   **Phase 1: Client-Side Persistence (Immediate Goal)**
        *   **Strategy**: Use `localStorage` to store unread status per user, per conversation.
        *   **Details**:
            *   A new state variable `persistentUnreadInfo` in `app-context.tsx` will hold `Record<conversationId, { unreadCount: number; lastReadMessageTimestamp?: string; }>`.
            *   This state will be loaded from `localStorage` (e.g., `unreadStatus_USER_ID`) on app start (when `authUser` is available) and saved back whenever it changes.
            *   When `loadWorkspaceData` in `app-context.tsx` initializes the `workspace` state:
                *   For each channel/DM, its `unreadCount` will be initialized based on `persistentUnreadInfo` and by comparing the channel/DM's `lastMessageTimestamp` (from DB) with the `lastReadMessageTimestamp` stored in `persistentUnreadInfo`.
                *   If `db.lastMessageTimestamp > localStorage.lastReadMessageTimestamp`, it indicates new messages arrived while the app was closed. The `unreadCount` will be set to `(localStorage.unreadCount || 0) + 1` (or a similar heuristic) to ensure the badge shows.
            *   The Realtime message handler in `app-context.tsx` will update both the `workspace` state's `unreadCount` and the `persistentUnreadInfo` for incoming messages in non-active conversations.
            *   Functions like `markConversationRead`, `setCurrentChannel`, and `setCurrentDirectMessage` in `app-context.tsx` will be updated to:
                *   Set `unreadCount: 0` in both `workspace` state and `persistentUnreadInfo`.
                *   Update `lastReadMessageTimestamp` in `persistentUnreadInfo` with the timestamp of the latest message in the conversation. This requires ensuring the latest message's timestamp is available when these functions are called (messages for the active conversation should be loaded).
        *   **Current Message Fetching Context**:
            *   `getInitialWorkspaceDataForUser` fetches channel/DM metadata including `last_message_timestamp` (global for the conversation, from DB) but does not fetch messages.
            *   `getMessagesForChannel`/`getMessagesForDirectMessage` fetch all top-level messages for a conversation when it's selected. This is when the exact `lastReadMessageTimestamp` can be determined.
            *   The `last_message_timestamp` on `Channels` and `Direct_Message_Sessions` tables is global, not per-user. The client-persisted `lastReadMessageTimestamp` is per-user (and per-browser for Phase 1).
    *   **Phase 2: Server-Side Persistence (Future Enhancement)**
        *   **Strategy**: Store read states in a dedicated database table (e.g., `user_conversation_read_states`) to enable cross-device sync.
        *   **Details**: Involves schema changes, RPC functions for marking read, and updating data fetching logic to calculate unread counts based on server-stored read states.

**2. Implement Real-time Message Updates (High Priority for Chat Experience - In Progress)**:
    *   The `useEffect` hook for Supabase Realtime on the `messages` table in `app-context.tsx` is partially implemented.
    *   **Next Steps**:
        *   Ensure robust handling of new messages: adding to local state, updating `unreadCount` (for both `workspace` state and `persistentUnreadInfo` as per item 1), and correctly identifying target channel/DM/thread.
        *   Handle potential duplicates if optimistic updates are also in place (current logic seems to attempt this).
        *   Thoroughly test multi-user scenarios.

**3. Enhance Message Fetching Strategy (Medium Priority, Post Unread/Realtime Basics)**
    *   **Problem**: Currently, `getMessagesForChannel` and `getMessagesForDirectMessage` fetch all top-level messages, which is inefficient for long histories.
    *   **Proposed Solution (as per original plan document - Section VI)**:
        *   Implement **Delta Sync**: Maintain a `last_fetched_message_timestamp` locally (e.g., in `localStorage` or `IndexedDB`) per channel/DM. Modify fetching functions to retrieve messages newer than this timestamp.
        *   Implement **Initial Load/Backfill**: For conversations with no local timestamp or for fetching older history, fetch the latest `N` messages and implement pagination/infinite scroll.
    *   **Relation to Unread Logic**: This is complementary. The `lastReadMessageTimestamp` for unread status and a `lastFetchedMessageTimestamp` for sync would work together.

**4. Verify Downstream Functionality (Ongoing)**:
    *   Thoroughly test DM message sending, optimistic updates, and display in `MessageList.tsx`.
    *   Re-verify channel message sending and display.
    *   Test multi-user interactions extensively.

**5. Address React Hooks Warning (Resolved 2025-05-22)**:
    *   **Issue**: Console warning `Warning: React has detected a change in the order of Hooks called by MainContent.`
    *   **Resolution**: The warning was caused by calling `useApp()` (which uses `useContext`) inside `.filter()` and `.map()` methods within the `MainContent.tsx` component when rendering direct message participant names. This violated the Rules of Hooks.
    *   **Fix**: Modified `MainContent.tsx` to use the `workspace` object already destructured from a top-level `useApp()` call, instead of calling `useApp()` again within the array methods. This ensures hooks are called in a consistent order. The warning is now resolved.

**6. Address `workspace_users` RLS Policy Limitation (Potential Future Action)**:
    *   The current `workspace_users` SELECT policy (`USING (user_id = auth.uid())`) is restrictive.
    *   **Action**: If broader visibility of workspace members is needed, investigate and implement a more advanced RLS solution without reintroducing recursion.

**7. Complete Data Fetching in `getInitialWorkspaceDataForUser` (Ongoing)**:
    *   Ensure `directMessages` fetching includes all necessary participant profile details and `last_message_timestamp`.
    *   Fetch and populate `channelTopics` for each channel.
    *   Consider fetching initial messages for the default/last active channel (once message fetching strategy in item 3 is improved).

**8. Implement Thread Fetching/Display**:
    *   Modify `getMessagesForChannel`/`DM` to also fetch replies (messages with `parent_message_id`), ideally with pagination.
    *   Update UI components to display threads.

~~**9. Refactor Client-Side Data Usage**:~~
    ~~*   Systematically replace all remaining direct usages of `mock-data.ts` in components with data from `useApp()` context.~~

**9. Mock Data Removal - COMPLETED (2025-01-21)**:
    *   **All mock data usages have been systematically removed** from components and replaced with data from `useApp()` context.
    *   **Mock data file deprecated:** `src/lib/mock-data.ts` renamed to `src/lib/mock-data.DEPRECATED.ts` to prevent imports.
    *   **Utility functions created:** `formatTimestamp` moved to `src/lib/utils.ts`, and local `findUserById` helpers added to components.
    *   **Clean architecture achieved:** Application now exclusively uses real backend data, clearly identifying what needs backend implementation.

**10. Implement Core CRUD for Other Entities**:
    *   Workspace Creation, Channel Creation, Topic Creation, etc.

**11. Review and Refine Assumed RLS Policies**:
    *   Review existing RLS policies against application requirements.

**12. File Storage & Enhanced Attachments**: Implement the phased plan for file/image attachments as detailed in `FILE_IMAGE_UX_IMPROVEMENT_PLAN.md`.
    *   **Initial Phase:** Client-side handling for pasted/dropped images (base64) and text file content, with backend support to store this data temporarily in `public.files.url` via an enhanced `send_message` RPC.
    *   **Definitive Phase:** Integrate Supabase Storage for actual file uploads, updating client-side upload logic and backend to use Supabase Storage URLs in `public.files.url`.

This updated plan should provide a clear overview of what's been accomplished and the path forward.

### I. Complete Mock Data Removal (Session of 2025-01-21) - Completed:

*   **Objective**: Remove all mock data fallbacks to ensure the application exclusively uses real backend data, making it clear what functionality still needs backend implementation.
*   **Strategy**: Systematically remove all imports and usages of `src/lib/mock-data.ts` from the codebase and replace with real data sources.
*   **Actions Taken**:
    1.  **Mock Data File Deprecation**:
        *   Renamed `src/lib/mock-data.ts` to `src/lib/mock-data.DEPRECATED.ts` to prevent imports.
        *   This immediately broke all existing imports, making it clear which files still depended on mock data.
    2.  **Utility Function Migration**:
        *   Moved `formatTimestamp` function from mock-data to `src/lib/utils.ts`.
        *   Updated all components to import `formatTimestamp` from utils instead of mock-data.
    3.  **Helper Function Replacement**:
        *   Replaced `findUserById` imports from mock-data with local helper functions in each component.
        *   Each component now uses `workspace?.users.find(u => u.id === userId)` to find users from the current workspace.
    4.  **App Context Cleanup**:
        *   Removed all mock workspace fallbacks from `src/lib/app-context.tsx`.
        *   Removed mock data references from optimistic updates and error handling.
        *   Removed mock data imports and usage from functions like `updateUserSetting`, `updateWorkspaceSettings`, and `updateUserStatus`.
    5.  **Component Updates**:
        *   Updated the following components to remove mock data dependencies:
            *   `src/components/Message.tsx`
            *   `src/components/WorkspaceMenu.tsx`
            *   `src/components/ChannelDetailsDialog.tsx`
            *   `src/components/channel-tabs/ChannelMembers.tsx`
            *   `src/components/channel-tabs/ChannelFiles.tsx`
            *   `src/components/project-view/ProjectTopics.tsx`
            *   `src/components/project-view/ProjectActivity.tsx`
            *   `src/components/project-view/ProjectOverview.tsx`
            *   `src/components/project-view/ProjectAnalytics.tsx`
*   **Outcome**:
    *   The application now **exclusively uses real backend data** from Supabase.
    *   Any functionality that doesn't work now clearly indicates missing backend support.
    *   Clean architecture achieved with no mixing of mock and real data.
    *   Better development path - it's obvious what needs to be implemented next.
    *   Improved testing capabilities with real data flows.
*   **Benefits**:
    *   **Clear Error Identification**: Features that break now need proper backend implementation.
    *   **Clean Architecture**: No more confusion between mock and real data sources.
    *   **Better Testing**: Can now test with real data flows exclusively.
    *   **Development Clarity**: Obvious what backend features are still missing.

### J. IndexedDB/Dexie Stability & Hybrid Caching Implementation (Session of 2025-01-21) - Completed:

*   **Problem Identified**: Severe IndexedDB corruption issues causing multi-second app loading delays and repeated "Dexie: Workaround for Chrome UnknownError on open()" messages. The corruption was profile-specific (affecting only one Chrome profile while others remained functional) with "Internal error opening backing store for indexedDB.open" errors that couldn't be resolved through normal Dexie recovery mechanisms.

*   **Root Cause Analysis**:
    *   **System-Level IndexedDB Failure**: Complete browser-level IndexedDB corruption that was beyond what Dexie or any wrapper could fix.
    *   **Profile-Specific Storage Corruption**: Each Chrome profile has isolated IndexedDB storage, and corruption was limited to one profile due to factors like storage quota issues, disk space problems, Chrome crashes during IndexedDB writes, or extension interference.
    *   **Inadequate Fallback Strategy**: No graceful degradation when IndexedDB became completely unavailable.

*   **Solution Implemented - Hybrid Caching System**:
    1.  **localStorage Fallback Cache (`src/lib/localStorage-cache.ts`)**:
        *   Complete localStorage-based caching system as fallback when IndexedDB fails.
        *   Intelligent quota management with automatic cleanup when storage limits are reached.
        *   Type-safe operations for all data types (profiles, messages, read states, etc.).
        *   Health checking and cache size monitoring capabilities.
        *   Global debugging functions accessible via browser console.

    2.  **Enhanced Database Layer (`src/lib/db.ts`)**:
        *   Robust error handling with retry logic and exponential backoff.
        *   Automatic recovery mechanisms including database recreation for severe corruption.
        *   Health monitoring to detect database state and graceful degradation when IndexedDB is unavailable.
        *   Global debugging utilities for manual database management.

    3.  **Hybrid Cache Operations (`src/lib/supabase-data-provider.ts`)**:
        *   Seamless fallback system that tries IndexedDB first, then localStorage.
        *   Transparent operation - application code doesn't need to know which storage is being used.
        *   All cache operations updated to use the hybrid approach.
        *   Consistent error handling across all data operations.

    4.  **App-Level Integration (`src/lib/app-context.tsx`)**:
        *   Database initialization on app startup with health checks.
        *   User notifications when falling back to localStorage mode.
        *   Graceful handling of storage failures without breaking the app.

*   **Key Features of the Solution**:
    *   **Automatic Fallback**: When IndexedDB fails, automatically switches to localStorage.
    *   **Transparent Operation**: App continues to work normally regardless of storage backend.
    *   **Smart Quota Management**: Automatically manages localStorage space limits.
    *   **Health Monitoring**: Continuous monitoring of storage system health.
    *   **Developer Tools**: Console functions for debugging and manual management.
    *   **Performance Optimized**: Minimal overhead when IndexedDB is working normally.

*   **Available Console Commands**:
    ```javascript
    // Check database health
    checkThreadFlowDB()

    // Reset IndexedDB (if recoverable)
    resetThreadFlowDB()

    // Clear localStorage cache for current user only
    clearThreadFlowCache()

    // Clear localStorage cache for ALL users (admin function)
    clearAllUsersThreadFlowCache()

    // Get cache information for current user
    getThreadFlowCacheInfo()

    // Get cache information for all users
    getAllUsersThreadFlowCacheInfo()
    ```

*   **Outcome**:
    *   **Eliminated Loading Issues**: No more multi-second delays from Chrome workarounds.
    *   **Improved Reliability**: App never fails due to IndexedDB issues.
    *   **Better User Experience**: Clear feedback when storage issues occur with blue info toast for simplified caching mode.
    *   **Future-Proof Architecture**: Can easily add other storage backends if needed.
    *   **Profile-Agnostic**: Works regardless of which Chrome profile has storage issues.

*   **Benefits**:
    *   **Resilience Against Storage Corruption**: Handles future storage corruption gracefully.
    *   **Consistent Performance**: Fast startup regardless of browser storage health.
    *   **Easier Debugging**: Clear error messages and console tools for troubleshooting.
    *   **Cross-Environment Compatibility**: Works consistently across all browser profiles and environments.

*   **Critical Fix Applied (2025-01-21) - BOTH localStorage AND IndexedDB**:
    *   **Problem Identified**: Cache data was NOT isolated per user in BOTH storage systems, causing serious data conflicts where User A's cached data would be visible to User B.
    *   **Root Cause**:
        *   **localStorage**: Message cache keys (`threadflow_messages_channel_${channelId}`, `threadflow_messages_dm_${dmId}`) were not user-specific.
        *   **IndexedDB**: Database name (`ThreadFlowSocialDB`) was shared across all users, with no user isolation at all.
    *   **Solution Implemented**:
        *   **localStorage Cache (`src/lib/localStorage-cache.ts`)**:
            *   Modified `LocalStorageCache` class to include `currentUserId` property and `setCurrentUser()` method.
            *   Updated all cache key generation to include user ID: `threadflow_${userId}_${table}_${id}`.
            *   Added user-specific cache management methods (`clearAll()` now only clears current user's cache).
            *   Enhanced debugging functions to support both per-user and all-users operations.
        *   **IndexedDB Database (`src/lib/db.ts`)**:
            *   Modified `AppDexie` constructor to accept `userId` parameter.
            *   Updated database name generation to be user-specific: `ThreadFlowSocialDB_${userId}`.
            *   Added `switchDatabaseUser()` function to change database when user changes.
            *   Updated `initializeDatabase()` to accept userId and switch to user-specific database.
            *   Updated `app-context.tsx` to initialize database with current user ID.
    *   **Impact**: Ensures complete data isolation between users in BOTH storage systems, preventing cache conflicts and data leakage across all cached data types (messages, profiles, workspaces, etc.).

### G. UI Behavior Enhancements (Session of 2025-05-22) - Completed:

*   **Problem Addressed**: The application exhibited an aggressive "reload" behavior whenever the browser tab regained focus. This was perceived as a full app refresh, even for brief tab switches.
*   **Root Cause Analysis**:
    1.  **Explicit Workspace Data Refresh**: `src/lib/app-context.tsx` contained a `useEffect` hook (triggered by `useVisibilityChange`) that re-fetched all initial workspace data (`getInitialWorkspaceDataForUser`) every time the tab became visible.
    2.  **Timed Loading Overlay**: The same `app-context.tsx` also displayed a timed loading overlay (`AppLoadingScreen variant="minimal"`) for 1 second on every tab refocus, again triggered by `useVisibilityChange`.
    3.  **Auth Profile Re-fetch**: `src/lib/auth-context.tsx` was re-fetching the user's profile on tab focus due to Supabase's default session refresh behavior.
*   **Solution Implemented**:
    1.  **In `src/lib/app-context.tsx`**:
        *   The `useEffect` hook responsible for the full workspace data re-fetch on tab focus (previously lines 234-285) was commented out.
        *   The conditional rendering block that showed the timed minimal loading screen (previously lines 1170-1182) was removed.
    2.  **In `src/lib/auth-context.tsx`**:
        *   Throttling logic was added to the user profile fetching mechanism. The profile is now only re-fetched if the user changes, no profile is currently loaded, or if more than 15 minutes have passed since the last successful fetch for the current user.
*   **Outcome**: These changes significantly reduce the "app reload" sensation on tab focus. The application now relies more on existing real-time subscriptions for data updates and avoids unnecessary full data re-fetches and disruptive loading indicators during frequent tab switches, leading to a smoother user experience.

### H. Refactoring `app-context.tsx` (Session of 2025-05-22):
*   **Objective**: To improve maintainability and readability of the large `src/lib/app-context.tsx` file.
*   **Strategy**: Extract pure state update logic into utility functions within a new file, `src/lib/app-context-utils.ts`.
*   **Actions Taken**:
    1.  Created `src/lib/app-context-utils.ts`.
    2.  Moved the following logic from `app-context.tsx` into utility functions in `app-context-utils.ts`:
        *   `deduplicateDirectMessages`: For cleaning up DM lists.
        *   `findSectionById`, `findChannelById`: For locating entities in workspace data.
        *   `transformSupabaseMessage`: For converting raw Supabase message objects (from Realtime or RPC) to the client-side `Message` type.
        *   `findOrCreateThreadFromContext`: For finding or initializing a `Thread` object.
        *   `updateOptimisticMessageInArray`: For updating a temporary optimistic message with its final version from the database.
        *   `mergeWorkspaceData`: For merging newly loaded workspace data with existing state while preserving dynamic content.
        *   `applyRealtimeMessageToWorkspace`: For processing incoming Realtime messages and updating the workspace state (adding messages, updating unread counts).
        *   `applyOptimisticMessageUpdate`, `revertOptimisticMessageUpdate`: For applying and reverting optimistic message additions in the UI.
        *   `applyReactionUpdateToWorkspace`: For updating message reactions in the state.
        *   `applyAddChannelUpdateToWorkspace`, `applyAddSectionUpdateToWorkspace`, `applyAddDirectMessageUpdateToWorkspace`: For state updates related to adding new entities.
        *   `applyUpdateChannelToWorkspace`: For updating an existing channel's data.
        *   `applyMarkConversationReadToWorkspace`: For marking channels or DMs as read (clearing unread counts).
        *   `applyUpdateUserSettingToWorkspace`, `applyUpdateWorkspaceSettingsToWorkspace`, `applyUpdateUserStatusToWorkspace`: For updating user and workspace settings/status.
        *   `applyNavigateToChannelTopicToWorkspace`, `applyClearActiveChannelTopicToWorkspace`: For managing active topics within channels.
        *   `applySetCurrentSectionToWorkspace`, `applySetCurrentChannelToWorkspace`, `applySetCurrentDirectMessageToWorkspace`, `applySetActiveThreadToWorkspace`: For handling navigation and active item selections.
    3.  Updated `src/lib/app-context.tsx` to import and use these utility functions, significantly reducing the inline complexity of its state update logic.
    4.  Corrected type definitions in `src/lib/types.ts` (e.g., `Message.edited` to `Message.editedTimestamp`) and ensured necessary types were imported into `app-context-utils.ts` to resolve TypeScript errors.
*   **Outcome**: The `setWorkspace` calls within `AppProvider` are now much simpler, delegating complex state transformations to the well-defined utility functions in `app-context-utils.ts`. This improves the modularity, readability, and testability of the application's core context logic.
*   **Next Steps for Refactoring**: Further refactoring could involve creating custom React Hooks (e.g., `useNavigation`, `useRealtime`, `useSearch`) to encapsulate related blocks of state, effects, and actions currently still in `app-context.tsx`.
### K. RLS Fix for Channel Creator Membership (2025-05-27):

*   **Problem**: Non-admin users, when creating a new channel, encountered a permission error toast ("Permission denied: You don't have the necessary rights to create channels in this workspace"). Although the channel was created, the subsequent step to add the creator as a member of the new channel failed.
*   **Root Cause Analysis**: The `INSERT` RLS policy on the `public.channel_members` table was too restrictive, only allowing workspace admins to add members to channels. This prevented non-admin channel creators from being automatically added to the `channel_members` table for the channel they just created. The `INSERT` RLS policy on the `public.channels` table correctly allowed any workspace member to create channels.
*   **Solution Implemented**:
    *   The `INSERT` RLS policy on `public.channel_members` ("Workspace admins can manage channel members") was modified.
    *   The updated policy now allows an `INSERT` if either:
        1.  The acting user is a workspace admin of the channel's workspace (original condition).
        2.  The `user_id` being inserted into `channel_members` matches `auth.uid()` (i.e., the user is adding themselves), AND the user is a member of the workspace to which the channel belongs.
    *   This change was applied via the `update_channel_members_insert_policy_fix` migration.
*   **Outcome**: Non-admin users can now successfully create channels, and they are automatically added as members to those channels without any permission errors. The misleading error toast related to channel creation rights (which was actually a channel membership issue) is resolved.

## XIII. Revised Implementation Roadmap & Future Enhancements (as of 2025-05-22)

This section outlines the revised and more comprehensive roadmap based on a thorough review conducted on 2025-05-22. It addresses key weaknesses in message fetching/synchronization, unread message tracking, persistence of user/workspace settings, feature gaps compared to mock data, offline support, and incorporates a strategy for E2E testing.

### A. Overall Goals for Revised Roadmap:
*   Achieve efficient and scalable message fetching and synchronization.
*   Implement robust and persistent unread message tracking (client-side initially, then server-side).
*   Ensure user data, preferences, and workspace settings are persisted on the backend.
*   Bridge feature gaps identified by comparing with `mock-data.ts` and recent investigations (e.g., admin adding users to workspace, section/channel archiving, adding file links).
*   Improve offline capabilities and graceful error handling for network issues.
*   Establish a comprehensive E2E testing strategy for ongoing development and stability.
*   **Note on Total Message Counts:** The database does not store a pre-calculated total message count per conversation. Fetching a live `COUNT(*)` is generally performance-intensive and complex with RLS. Therefore, the current approach of not displaying a global total message count (and relying on "has more messages" indicators for pagination) is appropriate and will be maintained.
*   **Note on User View Info Persistence:** Client-side management of active workspace, channel, DM, and thread focus is robust and supported. Backend persistence of this UI state via the `user_app_state` table (Task 4.4) remains a future enhancement for cross-device consistency.

### B. Phased Implementation Plan:

#### Phase 0: Critical Backend Fixes (Pre-requisites)

**Goal:** Address foundational backend issues necessary for reliable client-side functionality.

*   **Task 0.1: Implement DB Trigger for `last_message_timestamp` - Verified Complete (2025-05-25)**
    *   **Description:** Create a PostgreSQL trigger on the `messages` table. After an `INSERT` into `messages`, this trigger should update the `last_message_timestamp` column on the corresponding `channels` or `direct_message_sessions` table to the `timestamp` of the new message.
    *   **Rationale:** Provides a reliable server-side indicator of the latest activity in a conversation, crucial for delta sync and initial unread calculation.
    *   **Finding (2025-05-25):** SQL query against `pg_trigger` on the live Supabase instance confirms that active triggers (e.g., `on_message_inserted`, `on_new_message_update_conversation_timestamp`) exist on the `messages` table with functions like `update_last_message_timestamp`, indicating this functionality is in place. The migration file `migrations/0001_update_last_message_timestamp_trigger.sql` likely contains the definition.
    *   **E2E Test:** Verify `last_message_timestamp` updates correctly after sending messages to channels and DMs. (This test should still be performed if not already comprehensively done).

#### Phase 1: Core Messaging & Unread Stability (Client Focus)

**Goal:** Make message loading efficient, unread status persistent across sessions (on the same browser), and improve basic network error resilience.

*   **Task 1.1: Client-Side Persistence for Unread Status (localStorage) - Verified Complete (2025-05-25)**
    *   **Description:** Implement the `persistentUnreadInfo` strategy using `localStorage`. Store `Record<conversationId, { unreadCount: number; lastReadMessageTimestamp: string; }>`.
    *   **Rationale:** Provides session persistence for unread badges on the same browser.
    *   **Finding (2025-05-25):** Code review of `src/lib/app-context.tsx` confirms:
        *   `persistentUnreadInfo` state exists.
        *   Loading from `localStorage` (key `unreadStatus_USER_ID`) on auth.
        *   Saving to `localStorage` on change.
        *   Initial unread calculation in `applyInitialUnreadCountsToWorkspaceData` uses `persistentUnreadInfo` (as a fallback to server-side read states).
        *   `persistentUnreadInfo` is updated on new realtime messages and when conversations are marked read.
    *   **Note:** This implementation also partially addresses Task 2.5 (Integrate Server-Side Read State Tracking) by fetching and utilizing `user_conversation_read_states` and calling the `mark_conversation_as_read` RPC.
    *   **E2E Test:** Verify unread badges persist after reload; verify initial unread calculation.

*   **Task 1.2: Basic Message Pagination/Limit & Robustness (Client & Server)**
    *   **Description:** Modify `getMessagesForChannel` and `getMessagesForDirectMessage` to fetch only the latest `N` messages initially. Implement a "Load More" button or basic scroll-to-top trigger to fetch the next `N` older messages. Enhance pagination robustness with keyset pagination.
    *   **Rationale:** Prevents fetching huge message histories at once, improving initial load time. Keyset pagination increases robustness against skipped/duplicate messages.
    *   **Sub-Task 1.2.A: Implement Keyset Pagination (ID for Tie-Breaking) - Completed (Verified 2025-05-25)**
        *   **Description:** Modify `getMessagesForChannel` and `getMessagesForDirectMessage` in `supabase-data-provider.ts` to include the message `id` in the sorting and filtering criteria. For fetching older messages, order by `(timestamp DESC, id DESC)` and use a `WHERE (timestamp, id) < (cursor_ts, cursor_id)` clause. Adapt similarly for fetching newer messages (`timestamp ASC, id ASC` and `(timestamp, id) > (cursor_ts, cursor_id)`).
        *   **Rationale:** Prevents potential skipped or duplicated messages when timestamps collide, ensuring a more stable pagination experience.
        *   **Finding (2025-05-25):** Code review of `src/lib/supabase-data-provider.ts` shows that current pagination logic relies solely on `timestamp` for ordering and filtering. `id` is not used for tie-breaking in queries, and `cursorId` is not a parameter in fetching functions.
        *   **Dev Tasks (Client - `supabase-data-provider.ts`):**
            1.  Modify fetching functions to accept `cursorId`.
            2.  Update Supabase queries to implement keyset pagination.
        *   **Dev Tasks (Client - `app-context.tsx`):**
            1.  Manage state for `cursorId` per conversation.
            2.  Pass `cursorId` to data fetching functions.
        *   **E2E Test:** Verify pagination works correctly with identical timestamps.
    *   **Sub-Task 1.2.B: Review and Adjust `MESSAGE_FETCH_LIMIT` - Completed (Value set to 25) (Verified 2025-05-25)**
        *   **Description:** Evaluate the current `MESSAGE_FETCH_LIMIT` (initially 10 in `app-context.tsx`) for loading older messages. Consider increasing it (e.g., to 25 or 30). The `DELTA_MESSAGE_FETCH_LIMIT` (50) for newer messages can also be reviewed.
        *   **Rationale:** Improve user experience by fetching slightly larger chunks.
        *   **Finding (2025-05-25):** `src/lib/app-context.tsx` defines `MESSAGE_FETCH_LIMIT = 10` and `DELTA_MESSAGE_FETCH_LIMIT = 50`.
        *   **Dev Tasks (Client - `app-context.tsx`):**
            1.  Discuss and decide on a new `MESSAGE_FETCH_LIMIT`.
            2.  Update the constant if adjustment is desired.
        *   **E2E Test (Manual/UX):** Test "load more" behavior if limit is changed.
    *   **E2E Test (Overall):** Verify N messages load initially; "load more" works.

*   **Task 1.3: Basic Network Error Handling in Data Fetching - Completed (Dev Review & E2E Test Passed 2025-05-25)**
    *   **Description:** Wrap critical Supabase calls in `supabase-data-provider.ts` in try/catch blocks. `app-context.tsx` should handle these errors gracefully (e.g., toast notifications, fallback data).
    *   **Rationale:** Prevents unhandled promise rejections and improves user experience during network issues.
    *   **Finding (2025-05-25):**
        *   `supabase-data-provider.ts`: Functions like `getUserProfile`, `getMessagesForChannel`, `getMessagesForDirectMessage` include `try/catch` blocks, log errors, and return `null` on failure.
        *   `app-context.tsx`:
            *   `loadWorkspaceData` uses `Promise.allSettled`, logs errors, falls back to mock data, and shows a `toast.error`.
            *   Message fetching effects check for `null` responses from the provider and show `toast.error`.
            *   Realtime subscription errors are handled with a `toast.warning`.
    *   **Dev Tasks (Client - `supabase-data-provider.ts`, `app-context.tsx`):**
        1.  Review existing error handling for completeness and consistency.
        2.  Ensure all critical fetch points are covered.
    *   **E2E Test (Manual/Simulated):** Simulate offline mode and observe app behavior. - Passed

#### Phase 2: Enhanced Sync, Server-Side Unread Foundation & Offline Foundation

**Goal:** Implement true delta sync for messages, lay the groundwork for server-side unread tracking, and establish basic offline viewing capabilities using a client-side cache.

*   **Task 2.1: Implement Delta Sync for Messages - Verified Complete (2025-05-25)**
    *   **Description:** Enhance message fetching to only get messages newer than the `last_fetched_message_timestamp` stored locally per conversation.
    *   **Rationale:** Minimizes data transfer.
    *   **Finding (2025-05-25):**
        *   `supabase-data-provider.ts`: `getMessagesForChannel`/`DM` accept `sinceTimestamp` and query appropriately (`.gt('timestamp', sinceTimestamp)`).
        *   `app-context.tsx`:
            *   `last_fetched_message_timestamp` is stored in `localStorage` (keys like `last_fetched_ts_CONVOID`).
            *   `fetchNewerMessagesForConversation` uses this stored timestamp to fetch newer messages.
            *   Timestamps are updated after fetching new messages and also on receiving realtime messages.
    *   **E2E Test:** Verify only new messages are fetched when reopening a channel.

*   **Task 2.2: Introduce IndexedDB for Client-Side Caching - Completed (2025-05-25)**
    *   **Description:** Implemented a basic IndexedDB cache using `dexie.js` for fetched messages, profiles, workspaces (core details), channels (core details), direct message sessions (core details), and user conversation read states.
    *   **Rationale:** Enables offline viewing and faster perceived loads by attempting to serve data from cache first.
    *   **Implementation Details (2025-05-25):**
        *   **Library Integration:** Added `dexie` and `dexie-react-hooks` dependencies.
        *   **Schema Definition:** Created `src/lib/db.ts` defining `AppDexie` with tables for `profiles`, `workspaces`, `channels`, `directMessageSessions`, `messages`, `channelTopics`, and `userConversationReadStates`. Schemas include appropriate primary keys and indexes.
        *   **Data Provider Modification:** Updated `src/lib/supabase-data-provider.ts`:
            *   `getUserProfile`: Now attempts to fetch from `db.profiles` first. If cache miss, fetches from Supabase and populates cache.
            *   `getInitialWorkspaceDataForUser`: After fetching from Supabase, it now caches core details of the workspace, profiles of users in the workspace and DMs, core details of channels, and core details of direct message sessions into their respective Dexie tables.
            *   `getMessagesForChannel` & `getMessagesForDirectMessage`: After fetching messages from Supabase, they are now cached in `db.messages`. (Note: These functions do not yet *read* from the message cache first; this would be a further enhancement for full offline message viewing).
            *   `getUserConversationReadStates`: Now attempts to fetch from `db.userConversationReadStates` first. If cache miss, fetches from Supabase and populates cache.
            *   `getChannelTopics`: Added basic structure to attempt cache read and populate cache, though Supabase fetch logic for topics is still a placeholder.
        *   **App Context:** No direct changes were required in `src/lib/app-context.tsx` for this phase, as the caching logic is encapsulated within the data provider.
    *   **Dev Tasks (Client):** (Completed for this iteration)
        1.  Selected and integrated `dexie.js`.
        2.  Defined IndexedDB schemas in `src/lib/db.ts`.
        3.  Modified `supabase-data-provider.ts` to write to IndexedDB after Supabase fetches for key entities and read from cache for profiles and read states.
    *   **Next Steps/Enhancements:**
        *   Implement logic in `getMessagesForChannel`/`DM` to *read* from `db.messages` first.
        *   Implement cache-first strategy for `getInitialWorkspaceDataForUser` (currently it always fetches from Supabase then caches).
        *   Develop a cache invalidation/staleness strategy.
    *   **E2E Test:** Load data, go offline, reload app – verify viewability (partially testable for profiles/read states; full message viewability pending read-from-cache implementation for messages).

*   **Task 2.3: Design and Implement `user_conversation_read_states` Table (Server) - Verified Complete (2025-05-25)**
    *   **Description:** Create `user_conversation_read_states` table (`user_id`, `conversation_id`, `conversation_type`, `last_read_message_timestamp`).
    *   **Rationale:** Foundation for server-side, cross-device unread sync.
    *   **Finding (2025-05-25):**
        *   `migrations/0002_create_user_conversation_read_states.sql` exists in `migrations_list.json`.
        *   `execute_sql` confirms table `public.user_conversation_read_states` exists.
        *   `execute_sql` confirms columns: `user_id` (uuid), `conversation_id` (uuid), `conversation_type` (text), `last_read_message_timestamp` (timestamp with time zone), matching the plan.
    *   **Dev Tasks (Server - SQL):** (Completed)

*   **Task 2.4: Implement RPC to Update `user_conversation_read_states` (Server) - Verified Complete (2025-05-25)**
    *   **Description:** Create RPC `mark_conversation_as_read(...)` to `UPSERT` into `user_conversation_read_states`.
    *   **Rationale:** Allows clients to inform server of read progress.
    *   **Finding (2025-05-25):**
        *   `execute_sql` confirms RPC function `public.mark_conversation_as_read` exists.
        *   The fetched routine definition shows it performs an `INSERT ... ON CONFLICT ... DO UPDATE SET` into `user_conversation_read_states` with the correct parameters.
    *   **Dev Tasks (Server - SQL):** (Completed)

*   **Task 2.5: Integrate Server-Side Read State Tracking (Client) - Verified Complete (2025-05-25)**
    *   **Description:** Client calls `mark_conversation_as_read` RPC. On initial load, client fetches read states from `user_conversation_read_states` and uses this for unread counts.
    *   **Rationale:** Enables cross-device unread sync.
    *   **Finding (2025-05-25):** Code review of `app-context.tsx` and `supabase-data-provider.ts` confirms:
        *   `markConversationRead` function in `app-context.tsx` calls `supabase.rpc('mark_conversation_as_read', ...)`.
        *   `loadWorkspaceData` in `app-context.tsx` calls `getUserConversationReadStates` (from `supabase-data-provider.ts`) to fetch these states.
        *   `applyInitialUnreadCountsToWorkspaceData` in `app-context.tsx` uses these server-fetched states, prioritizing them over localStorage, for initial unread count calculation.
    *   **E2E Test:** Unread status syncs correctly across two simulated clients/browsers.

#### Phase 3: Feature Completeness & Backend Persistence

**Goal:** Implement backend persistence for remaining entities and features identified as gaps (vs. `mock-data.ts` or app vision).

*   **Task 3.1: Persist User Profile Details & Settings - Backend Implemented (2025-05-26); Settings Persistence Verified. Profile Details Persistence UI Blocked.**
    *   **Description:** Update `updateUserStatus` and `updateUserSetting` in `app-context.tsx` to call Supabase client methods (`.update()`) to modify the `profiles` table (specific columns for status/title/about, and the `settings` JSONB column for `UserSettings`).
    *   **Rationale:** Persist user customizations beyond basic auth info.
    *   **Status (2025-05-27):** **Completed.** Backend calls for `updateUserSetting` and `updateUserStatus` are implemented in `app-context.tsx`. Settings persistence (`updateUserSetting`) has been manually verified.
    *   **Note:** Full testing of profile details (status, title, about) persistence is blocked by a non-functional UI for viewing/editing the current user profile.
    *   **E2E Test:** User A updates their profile status/title/a specific setting. Reload app/login as user A, verify changes persist. Verify user B cannot update user A's profile. (Settings part testable, profile details part blocked by UI).

*   **Task 3.2: Persist Workspace Settings - Completed (2025-05-26)**
    *   **Note:** Non-admin UX (e.g., disabling/hiding settings controls, providing feedback on failed attempts) and persistence of other workspace properties (like 'name') are potential follow-up items but outside the scope of this specific task.
    *   **Description:** Update `updateWorkspaceSettings` in `app-context.tsx` to call Supabase client (`.update()`) to modify the `workspaces.settings` JSONB column.
    *   **Rationale:** Persist workspace customizations.
    *   **Status (2025-05-27):** **Completed.** Backend call for `updateWorkspaceSettings` in `app-context.tsx` was implemented on 2025-05-26.
    *   **E2E Test:** Workspace admin updates a workspace setting. Other members see the effect (if applicable, e.g., default theme). Setting persists on reload for all members. Non-admin cannot update.

*   **Task 3.3: Implement Backend for Section Creation & Management - Completed (2025-05-26)**
    *   **Description:** Modify `addSection` to insert into the `sections` table via Supabase client. Add functionality for updating (name, order) and deleting sections.
    *   **Rationale:** Persist sections and allow management.
    *   **Status (2025-05-27):** **Completed.** Backend calls for `addSection`, update, and delete were implemented on 2025-05-26.
    *   **E2E Test:** Create, rename, reorder, delete section. Verify persistence and RLS.
    *   **Note (Gap from Investigation):** Archiving for `Sections` themselves is not implemented. This could be a future consideration if the `is_archived` field is added to the `sections` table schema.

*   **Task 3.4: Implement Backend for DM Creation - Completed (2025-05-26)**
    *   **Description:** Modify `addDirectMessage`. If a DM session for the participant pair doesn't exist, call an RPC `create_direct_message_session(target_user_id uuid)` which creates a new `direct_message_sessions` record and adds both current user and target user to `direct_message_participants`. The RPC should return the new DM session details.
    *   **Rationale:** Persist DM sessions properly.
    *   **Status (2025-05-27):** **Completed.** The `create_direct_message_session` RPC and client-side integration in `addDirectMessage` were implemented on 2025-05-26.
    *   **E2E Test:** User A creates DM with User B. Both users see it. Persists on reload. Attempting to create again navigates to existing DM.

*   **Task 3.5: Implement Backend for Reactions - Completed (2025-05-26)**
    *   **Description:**
        *   Modify `addReaction` to `UPSERT` into `reactions` table (or use separate add/remove RPCs for atomicity if needed, e.g., `toggle_reaction(p_message_id uuid, p_emoji text)`).
        *   Modify message fetching (initial, paginated, delta sync) to also fetch aggregated reaction data for messages (e.g., using a database view or join: `message_id, emoji, count, user_ids_array`).
    *   **Rationale:** Persist reactions, show existing reactions from all users.
    *   **Finding (2025-05-25):**
        *   The `addReaction` function in `app-context.tsx` calls `applyReactionUpdateToWorkspace` (from `app-context-utils.ts`).
        *   `applyReactionUpdateToWorkspace` only updates the local client-side state and does not make database calls to persist reactions.
        *   Message fetching functions (`getMessagesForChannel`, `getMessagesForDirectMessage` in `supabase-data-provider.ts`) do not currently fetch aggregated reaction data.
    *   **Dev Tasks (Server - SQL):** Potentially a view `message_reactions_summary (message_id, emoji, count, user_ids_array)`. RLS on `reactions` table (users can add/delete their own).
    *   **Dev Tasks (Client):** Update `addReaction` (likely within `applyReactionUpdateToWorkspace` or by modifying `addReaction` to call Supabase directly/via provider) with Supabase calls. Update message fetching in `supabase-data-provider.ts` to include reaction data. Update UI to display fetched reactions.
    *   **E2E Test:** User A adds a reaction. Reload, reaction persists. User B sees User A's reaction. User A removes reaction.

*   **Task 3.6: Implement Backend and Client for Channel Topics & Notes - Completed (2025-05-26)**
    *   **Description (Channel Topics):**
        *   Implement full lifecycle management for `channel_topics`, including Create, Read, Update, Delete, Archive, and Unarchive functionalities.
        *   Topics are optional; the system must function if they are not used.
        *   Manual topic management is the baseline, with LLM features as future enhancements.
        *   **Schema Updates:**
            *   `channel_topics`: Add `is_archived BOOLEAN DEFAULT FALSE`, `archived_at TIMESTAMPTZ NULL`, `archived_by UUID NULL REFERENCES profiles(id)`.
            *   `messages`: Add `is_archived BOOLEAN DEFAULT FALSE`, `archived_at TIMESTAMPTZ NULL`, `archived_by UUID NULL REFERENCES profiles(id)`.
        *   **Backend (RPCs):**
            *   `create_channel_topic(p_channel_id UUID, p_title TEXT, p_summary TEXT DEFAULT NULL)`
            *   `get_channel_topics(p_channel_id UUID, p_include_archived BOOLEAN DEFAULT FALSE)`
            *   `update_channel_topic(p_topic_id UUID, p_title TEXT DEFAULT NULL, p_summary TEXT DEFAULT NULL)`
            *   `delete_channel_topic(p_topic_id UUID)`: Will set `messages.topic_id = NULL` for associated messages before deleting the topic.
            *   `archive_channel_topic(p_topic_id UUID)`: Archives the topic and its currently associated messages.
            *   `unarchive_channel_topic(p_topic_id UUID)`: Unarchives the topic and messages previously archived with it.
        *   **RLS:** Review and update RLS for `channel_topics` and `messages` for these operations.
        *   **Client-Side:**
            *   Integrate RPC calls into `supabase-data-provider.ts`.
            *   Manage topic state (including archived status) in `app-context.tsx`.
            *   Update `ChannelTopics.tsx` to use real data, implement full CRUD and Archive/Unarchive UI, and filter/display archived topics.
            *   Update `NewMessageDialog.tsx` to allow selection of existing (non-archived) topics and implement placeholder topic creation (client calls `create_channel_topic` with generated title, then `send_message` with new topic ID).
            *   Ensure messages in focused topic views (`ChannelTopics.tsx`) are correctly filtered and new messages are associated with the selected topic.
    *   **Description (Channel Notes):**
        *   Add function to update `channels.channel_note` via Supabase client. (This part remains as originally planned).
        *   Channel Notes currently admin-only editable via RLS; member editing to be revisited.
    *   **Rationale:** Core feature for organizing channel discussions, with robust lifecycle management including archiving.
    *   **Status (2025-05-27):** **Completed.** Backend RPCs for channel topic CRUD and archive/unarchive, schema updates, and client-side integration were implemented on 2025-05-26. Channel note update functionality also implemented. Archiving for \`Channel_Topics\` and \`Messages\` is supported.
    *   **Note (Gap from Investigation):** Archiving for \`Channels\` themselves is not implemented. This could be a future consideration if the \`is_archived\` field is added to the \`channels\` table schema.
    *   **E2E Test:**
        *   Full CRUD and Archive/Unarchive for channel topics.
        *   Verify coupled message archiving/unarchiving with topics.
        *   Placeholder topic creation from `NewMessageDialog.tsx`.
        *   Message association and display within focused topic views.
        *   RLS for all topic operations.
        *   Edit and persist channel note.

*   **Task 3.7: Implement Backend for Workspace Creation - Completed (2025-05-26)**
    *   **Description:** Add UI and backend logic for a user to create a new workspace. This involves inserting into `workspaces` and `workspace_users` (making the creator an admin), ideally via an RPC.
    *   **Rationale:** Core application functionality.
    *   **Finding (2025-05-25):**
        *   Code review of `src/lib/app-context.tsx` shows no client-side function for initiating workspace creation with backend calls.
        *   `execute_sql` confirms no RPC function named `create_workspace` exists in the database.
    *   **Dev Tasks (Client & Server):** UI flow. Create RPC `create_workspace(p_name text, p_icon_url text default null)` that handles inserts into `workspaces` and `workspace_users`. Implement client-side function to call this RPC. RLS on `workspaces` (user can create), `workspace_users` (RPC handles initial admin).
    *   **E2E Test:** User creates a new workspace. It appears in their list. They are an admin.

    *   **Task 3.8: Implement Admin Feature to Add Users to Workspace - Pending**
        *   **Description:** Develop client-side UI and backend RPC for workspace admins to add existing users to their workspace.
        *   **Rationale:** Essential for workspace management.
        *   **Dev Tasks (Client & Server):** UI flow for admin to select a user and add to workspace. New RPC \`add_user_to_workspace(p_workspace_id UUID, p_user_id UUID, p_role TEXT DEFAULT 'member')\`. RLS on \`workspace_users\` to allow admins of \`p_workspace_id\` to insert.
        *   **E2E Test:** Admin adds User C to Workspace W. User C can access Workspace W.

#### Phase 4: Advanced Features & Refinements

**Goal:** Implement remaining major features and perform optimizations.

*   **Task 4.1: Enhanced File & Image Attachment UX and Storage Integration - Phased Implementation**
    *   **Description:** Implement the comprehensive file and image attachment UX as outlined in `FILE_IMAGE_UX_IMPROVEMENT_PLAN.md`. This is a multi-faceted task involving editor unification, "attachment pill" UI, and a phased approach to file storage.
    *   **Phase 4.1.A (Initial - Client & Backend): Editor Unification & Basic Attachments (Base64/Text Content)**
        *   Unify message input on `react-simplemde-editor`.
        *   Implement client-side UI for "attachment pills" below the editor.
        *   Handle pasted/dropped images (as base64) and text file content, adding them as `LocalAttachment` objects to client state.
        *   Enhance `send_message` RPC to accept this `LocalAttachment` data and create entries in `public.files`, storing base64/text content temporarily in `files.url`.
        *   Update message rendering to display these attachments.
        *   Ensure `ChannelFiles.tsx` can consume data from `public.files` populated this way.
    *   **Phase 4.1.B (Definitive - Client & Backend): Full Supabase Storage Integration**
        *   Implement client-side logic to upload files directly to a designated Supabase Storage bucket (e.g., `message_attachments`), with progress indication.
        *   On successful upload, create/update `public.files` entry with the actual Supabase Storage URL in `files.url`.
        *   Modify `send_message` RPC to link these pre-uploaded files (via their `file_id`s from `public.files`) to the new message.
        *   Update client-side attachment handling (paste, drop, "+" button) to use this new upload flow.
        *   Ensure `Message.tsx` and `ChannelFiles.tsx` render/link files using Supabase Storage URLs.
    *   **Rationale:** Deliver a significantly improved and modern file attachment experience, aligning with user expectations from apps like Slack/Discord. Phasing allows for quicker delivery of core UX improvements while building towards a robust storage solution.
    *   **Finding (2025-05-25):**
        *   `search_files` for `supabase.storage` in `src/**/*.tsx` yielded 0 results, indicating no client-side usage of the Supabase Storage SDK.
        *   Plan document (Section III.12, X) notes this feature is deferred and `files.url` is a placeholder.
    *   **Dev Tasks (Client & Server):**
        *   Client: Implement UI for upload/download. Use Supabase Storage JS client.
        *   Server: Define RLS for Storage buckets.
        *   Update `files` table CRUD to link to storage objects.
    *   **E2E Test:** Upload file to a channel message. Another member downloads/views it. Delete file.

*   **Task 4.2: Real-time for Other Entities (as needed) - Partially Implemented (Verified 2025-05-25)**
    *   **Description:** Add Supabase Realtime subscriptions for changes to user presence/status, workspace settings, channel metadata (name, description), reactions, etc.
    *   **Rationale:** Richer real-time experience beyond just new messages.
    *   **Finding (2025-05-25):**
        *   **Implemented:** `app-context.tsx` has a Realtime subscription for `INSERT` events on the `messages` table.
        *   **Pending:** Subscriptions for message edits/deletions, reactions, user presence/status, workspace settings, channel metadata, and typing indicators (as outlined in Section V) are not yet implemented.
    *   **Dev Tasks (Client & Server):** Identify entities needing RT. Set up subscriptions in `app-context.tsx`. Handle payloads.
    *   **E2E Test:** User A changes status; User B sees it update in real-time. Admin changes workspace theme; members see it update.

*   **Task 4.3: Advanced Search (Server-Side if needed) - Client-Side Implemented, Server-Side Pending (Verified 2025-05-25)**
    *   **Description:** Evaluate if current client-side search (which iterates over locally held messages) is sufficient. If not, implement server-side search using Postgres Full-Text Search on the `messages.content` (and other relevant fields) or an Edge Function.
    *   **Rationale:** Scalable and performant search across all messages, not just locally loaded ones.
    *   **Finding (2025-05-25):**
        *   `src/lib/app-context.tsx` contains a `performSearch` function that implements client-side search by iterating through messages loaded in the local state.
        *   There is no backend call for search functionality. Server-side search is not implemented.
    *   **Dev Tasks (Server & Client):** If server-side search is deemed necessary: create DB functions/indexes for FTS or an Edge Function. Update client to call this search endpoint.
    *   **E2E Test:** Search for terms across entire message history (if server-side implemented); verify results are accurate and performant.

*   **Task 4.4: Use `user_app_state` Table (Optional but Recommended) - Pending Implementation (Verified 2025-05-25)**
    *   **Description:** If cross-device UI state persistence (current channel, active theme, sidebar state, etc.) is desired, integrate reading/writing to the `user_app_state` table.
    *   **Rationale:** Consistent UX across devices/sessions.
    *   **Finding (2025-05-25):**
        *   The `user_app_state` table schema is defined in `supabase/database.types.ts`.
        *   Code review of `src/lib/app-context.tsx` shows no current logic for reading from or writing to the `user_app_state` table to persist UI state like current channel/DM selections.
    *   **Dev Tasks (Client & Server):**
        *   Client: On relevant UI state changes (e.g., `currentChannelId`, theme selection), implement calls (likely RPC) to update `user_app_state`. On application load, fetch from `user_app_state` to initialize these UI aspects.
        *   Server: Ensure RLS on `user_app_state` allows users to read/write their own state.
    *   **E2E Test:** User A sets a theme and navigates to a channel. Logs out. Logs in on a different browser/device. Verify theme and last channel are restored.

*   **Task 4.5: UI/UX Verification and E2E Tests for Section/Channel Visibility - Pending**
    *   **Description:** Conduct comprehensive UI/UX verification for all channel access scenarios (public, private-member, private-non-member) following the discoverability enhancements. Create/update specific E2E tests for these visibility and access rules.
    *   **Rationale:** Ensure the enhanced channel discoverability feature works as intended and is robustly tested.
    *   **Reference:** Section XII.D (Next Steps), Section XV.
    *   **Status:** Pending.

*   **Task 4.6: Implement Section Archiving - Pending**
    *   **Description:** Add functionality to archive and unarchive `Sections`. This would involve adding `is_archived`, `archived_at`, and `archived_by` fields to the `sections` table.
    *   **Rationale:** Allow users to hide or retire entire sections without permanently deleting them and their contained channels/topics/messages.
    *   **Dev Tasks (Client & Server):**
        *   Server: Update `sections` table schema. Implement RLS for archive/unarchive operations (likely admin-only). Consider if archiving a section should also archive its contents (channels, topics, messages) or if they become unsectioned/orphaned.
        *   Client: Add UI elements for admins to archive/unarchive sections. Update data fetching and display logic to handle archived sections (e.g., filter them out by default, provide an option to view archived sections).
    *   **E2E Test:** Admin archives a section. Section is hidden for non-admins. Admin can view and unarchive the section.

*   **Task 4.7: Implement Channel Archiving - Pending**
    *   **Description:** Add functionality to archive and unarchive `Channels`. This would involve adding `is_archived`, `archived_at`, and `archived_by` fields to the `channels` table.
    *   **Rationale:** Allow users to hide or retire channels without permanently deleting them and their content.
    *   **Dev Tasks (Client & Server):**
        *   Server: Update `channels` table schema. Implement RLS for archive/unarchive operations (e.g., channel owner or workspace admin). Consider if archiving a channel should also archive its topics and messages (similar to topic archiving).
        *   Client: Add UI elements to archive/unarchive channels. Update data fetching and display logic to handle archived channels.
    *   **E2E Test:** User archives a channel. Channel is hidden. User can view and unarchive the channel.

### C. E2E Testing Strategy - General Principles:

*   **Tooling:** Utilize a Node.js-based test runner (e.g., Jest) combined with the Supabase JS client. For UI-specific interactions that are hard to test programmatically via API/DB, Playwright can be considered for full browser automation.
*   **Test User Management:** Employ a system for creating and managing dedicated test users with different roles and permissions. The existing `seed_test_users.js` can be expanded for this.
*   **Data Isolation:** Ensure tests clean up after themselves or run against a dedicated test database/schema to maintain a consistent state. Supabase branching could be useful here.
*   **Scope & Types of Tests:**
    1.  **API/RPC Level Tests:** Directly call `supabase-data-provider.ts` functions or RPCs and assert database state changes or returned data. Excellent for testing CRUD and RLS for specific data operations.
        *   *Example (User Settings Update):*
            1.  Setup: Create test user A.
            2.  Action: As user A, call client function to update theme to 'dark'.
            3.  Assertion: Query `profiles` table (as admin/service_role) to verify `settings->>'theme'` is 'dark' for user A.
            4.  RLS Check: As test user B, attempt to update user A's theme; expect RLS error.
    2.  **RLS Policy Validation:** Design specific scenarios to directly test RLS policies.
        *   *Example (Channel Visibility):*
            1.  Setup: Create Workspace W, Channel C in W. User A is member of W, User B is not.
            2.  Action: As User A, attempt to select Channel C; expect success.
            3.  Action: As User B, attempt to select Channel C; expect RLS to prevent access (empty result or error).
    4.  **Core User Flow Simulation (CLI/Programmatic):** Simulate sequences of actions.
        *   *Example (Basic Message Send):*
            1.  Setup: User A and User B in Channel C.
            2.  Action: As User A, send message M to Channel C.
            3.  Assertion (User A): Message M appears in User A's client state (optimistic). Query DB, message M exists.
            4.  Assertion (User B): Message M is received via Realtime (if testing RT) or fetched on next poll/channel open.
    5.  **Offline Scenario Simulation (Challenging for CLI, better with UI E2E):**
        *   Programmatically, one could simulate by having the client load data, then prevent further network calls while asserting against cached (IndexedDB) data.
*   **Test Structure:** Organize tests by feature module (e.g., `auth.e2e.test.js`, `messaging.e2e.test.js`, `userSettings.e2e.test.js`).
*   **CI/CD Integration:** Automate test execution in the CI/CD pipeline to catch regressions early.
*   **Coverage:** Aim for high coverage of critical paths, RLS policies, and data modification points.

### D. Code Health and Maintainability (Ongoing):

*   **Task D.1: Refactor `app-context.tsx` using Custom Hooks**
    *   **Description:** To improve the manageability and readability of the large `src/lib/app-context.tsx` file, progressively extract cohesive blocks of state, effects, and callback functions into custom React hooks. Examples include `useNavigationHandler` (for navigation history and actions), `useSearchHandler` (for search state and logic), and `usePersistentUnreadInfo` (for managing unread counts in `localStorage`). The main `AppProvider` will then compose these hooks.
    *   **Rationale:** Enhances separation of concerns, makes the main provider cleaner, and can make individual pieces of logic easier to understand and potentially test. This is an internal refactoring that can be done incrementally without altering the external context API.
    *   **Dev Tasks (Client - `src/lib/` or `src/hooks/app-context/`):**
        1.  Identify suitable blocks of logic in `app-context.tsx`.
        2.  Create custom hooks for these blocks.
        3.  Update `AppProvider` to use these custom hooks, ensuring the context value provided by `useApp()` remains consistent.

## XIV. RLS and RPC Review (May 2025) - Completed

A comprehensive review of Row Level Security (RLS) policies and Remote Procedure Call (RPC) database functions was conducted in May 2025. Key security enhancements, bug fixes, and alignment adjustments were implemented.

*   **RLS Policies:** Corrections and security hardening were applied to policies for `channels`, `direct_message_participants`, `direct_message_sessions`, and `files` tables. These changes are reflected in the local `supabase/rls_*.sql` files and have been verified on the live Supabase project. Summaries of these changes are noted in Section V.
*   **RPC Functions:** Necessary corrections were made to functions including `send_message`, `create_direct_message_session`, channel topic management RPCs (security model and column names), and helper functions (e.g., `is_dm_participant`, `is_channel_member` for `search_path`). These changes are reflected in [`supabase/rpc_functions.sql`](supabase/rpc_functions.sql). Summaries of these changes are noted in Section VI.

The detailed recommendations and their original rationale from this review have been integrated or superseded by the implemented changes.

## XV. Section and Channel Visibility Enhancement Plan (May 2025) - Technical Implementation Completed

The core technical changes (Backend RLS and Client-Side Data Fetching modifications) outlined in this plan to enhance section and channel discoverability were completed in May 2025. A summary of these changes is included in Section XII.D under "Enhanced Section and Channel Discoverability (May 2025 - Technical Implementation Completed)".

Full UI/UX verification and the creation/update of specific E2E tests for these enhancements are tracked as ongoing tasks in the roadmap (Section XIII).
