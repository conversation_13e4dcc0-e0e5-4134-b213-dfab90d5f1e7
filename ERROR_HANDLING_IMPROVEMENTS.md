# Client-Side Error Handling Improvements

## Overview

This document describes the improvements made to client-side error messaging for privilege-related errors, particularly for row-level security (RLS) policy violations in Supabase operations.

## Problem

Previously, when users encountered privilege-related errors (such as trying to create sections without admin rights), they would see raw database error messages like:

```
Failed to create section: new row violates row-level security policy for table "sections"
```

These messages are:
- Technical and confusing for end users
- Don't provide actionable guidance
- Inconsistent across different operations
- Don't explain what permissions are needed

## Solution

### 1. Centralized Error Handling Utility (`src/lib/error-utils.ts`)

Created a comprehensive error handling utility that:

- **Detects privilege-related errors** by checking error codes and message patterns
- **Maps error codes** to user-friendly messages
- **Provides context-aware messaging** based on the operation being performed
- **Handles both expected and unexpected errors** consistently

### 2. Key Functions

#### `isPrivilegeError(error)`
Detects if an error is privilege/permission related by checking:
- Error code `42501` (RLS violation)
- Message patterns like "row-level security policy", "permission denied", etc.

#### `getUserFriendlyErrorMessage(error, context, fallback?)`
Returns appropriate user-friendly messages based on:
- Error type (privilege, unique constraint, etc.)
- Operation context (create section, update workspace, etc.)
- Custom fallback message

#### `handleSupabaseError(error, context, options)`
Complete error handling with:
- Automatic logging
- Toast notifications
- Context-aware messaging

### 3. Operation Contexts

Defined specific contexts for different operations:
- `CREATE_SECTION` - "create sections"
- `CREATE_CHANNEL` - "create channels"
- `UPDATE_WORKSPACE_SETTINGS` - "modify workspace settings"
- And more...

## Implementation

### Updated Functions

The following functions now use the improved error handling:

1. **`addSection`** - Section creation
2. **`updateSection`** - Section updates
3. **`deleteSection`** - Section deletion
4. **`addChannel`** - Channel creation
5. **`updateWorkspaceSettings`** - Workspace settings

### Before vs After

**Before:**
```typescript
if (error) {
  console.error('Error creating section in Supabase:', error);
  toast.error(`Failed to create section: ${error.message}`);
  return null;
}
```

**After:**
```typescript
if (error) {
  handleSupabaseError(error, OPERATION_CONTEXTS.CREATE_SECTION, {
    operation: 'section creation',
    fallbackMessage: 'Failed to create section'
  });
  return null;
}
```

## User Experience Improvements

### Privilege Errors
- **Old:** "Failed to create section: new row violates row-level security policy for table 'sections'"
- **New:** "Permission denied: You don't have the necessary rights to create sections in this workspace. Please contact your workspace administrator."

### Other Error Types
- **Unique Violations:** "This item already exists. Please choose a different name."
- **Foreign Key Violations:** "This operation cannot be completed due to related data constraints."
- **Missing Data:** "Required information is missing. Please fill in all required fields."

## Benefits

1. **User-Friendly Messages:** Clear, actionable error messages that users can understand
2. **Consistent Experience:** All privilege errors now show similar, helpful messages
3. **Actionable Guidance:** Users know to contact their workspace administrator
4. **Maintainable Code:** Centralized error handling reduces code duplication
5. **Extensible:** Easy to add new error types and contexts

## Testing

The error handling utility includes comprehensive tests covering:
- Privilege error detection
- Context-specific messaging
- Edge cases and fallbacks
- Real-world error scenarios

## Future Enhancements

1. **Internationalization:** Support for multiple languages
2. **Error Recovery:** Automatic retry mechanisms for transient errors
3. **User Guidance:** Links to help documentation or contact forms
4. **Analytics:** Error tracking for improving user experience

## Usage Examples

```typescript
// Basic usage
handleSupabaseError(error, OPERATION_CONTEXTS.CREATE_SECTION);

// With custom options
handleSupabaseError(error, OPERATION_CONTEXTS.UPDATE_SECTION, {
  operation: 'section update',
  fallbackMessage: 'Failed to update section',
  showToast: true,
  logError: true
});

// Just get the message without side effects
const message = getUserFriendlyErrorMessage(error, OPERATION_CONTEXTS.CREATE_CHANNEL);
```
