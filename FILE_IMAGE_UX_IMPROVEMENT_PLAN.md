# File & Image UX Improvement Plan

## 1. Overview

This plan details the strategy for enhancing file and image support within the chat application's message input experience. The goal is to provide a more intuitive and powerful way for users to attach and share files, converging the existing "New Message" dialog editor and the simpler "Message List" input box into a unified experience.

## 2. Core Goals

*   **Unified Editor Experience:** Utilize `react-simplemde-editor` (EasyMDE) as the base for all message composition, allowing for consistent Markdown support.
*   **Rich File/Image Input:**
    *   Support pasting images and files directly into the input area.
    *   Enable drag-and-drop for images and files.
    *   Provide a consistent "+" button for initiating attachments.
*   **"Attachment Pills" UI:** Display pending attachments as distinct "pills" below the text editor during composition, rather than inline.
*   **Phased Storage Strategy:**
    1.  **Initial Phase:** Handle pasted/dropped images as base64 encoded data and pasted/dropped plain text file *content*. These will be represented as pills and their data temporarily stored (e.g., base64 in `files.url`).
    2.  **Definitive Phase:** Implement robust uploads to Supabase Storage for all supported file types, with pills representing these uploads and linking to canonical Storage URLs.
*   **Backend Integration:** Ensure all attachments result in entries in the `public.files` table, correctly linked to messages and channels, to support features like the "Channel Files" view.
*   **UX Consistency:** Maintain a Slack/Discord-like feel for the input area, with configurable editor toolbar presence.

## 3. Key Components & Technologies

*   **Primary Editor:** `react-simplemde-editor` (wrapping EasyMDE).
*   **Supabase:**
    *   `public.files` table: For storing file metadata.
    *   `public.messages` table: For storing message content.
    *   Supabase Storage: For storing actual file objects (Definitive Phase).
    *   `send_message` RPC: To be enhanced for handling attachments.
*   **Client-Side Components:**
    *   `MessageInput.tsx` (to be refactored)
    *   `NewMessageDialog.tsx` (to be refactored)
    *   `AttachmentPill.tsx` (New: for displaying pending attachments)
    *   `app-context.tsx` (for `sendMessage` logic and state management)
    *   `supabase-data-provider.ts` (for file upload logic)
    *   `Message.tsx` (for rendering messages with attachments)
    *   `ChannelFiles.tsx` (for displaying all files in a channel)

## 4. Phased Implementation Plan (Client & Backend)

### Phase 1: Editor Unification & Basic Attachment Handling with "Pills"

*   **Objective:** Unify on `react-simplemde-editor`, introduce the "pill" UI, and handle pasted/dropped images (as base64) and text file *content*. **(COMPLETED)**
*   **Client-Side Tasks:**
    1.  **Component Structure Refactor (`MessageInput.tsx`, `NewMessageDialog.tsx`):** **(COMPLETED)**
        *   Adopt `SimpleMDE` as the text input component. **(COMPLETED)**
        *   Create a new "Attachment Pills Area" `div` below the `SimpleMDE` editor. **(COMPLETED)**
        *   Relocate the action bar (with "+" button, send button) below the Attachment Pills Area. **(COMPLETED)**
    2.  **`SimpleMDE` Configuration:** **(COMPLETED)**
        *   `MessageInput.tsx`: Minimal or no toolbar. **(COMPLETED)**
        *   `NewMessageDialog.tsx`: Can retain a more comprehensive toolbar or also adopt a minimal one, relying on the "+" button. **(COMPLETED)**
        *   Ensure auto-grow, styling, placeholder, and adapted `onKeyDown` (send/escape) work correctly. **(COMPLETED)**
        *   The editor itself will *not* render image/file previews inline during composition. **(COMPLETED)**
    3.  **State Management for Attachments (in parent component):** **(COMPLETED)**
        *   Introduce `pendingAttachments: LocalAttachment[]` state. **(COMPLETED)**
        *   Define `LocalAttachment` type: `{ id: string (local temp ID), name: string, type: string, size: number, dataUrl?: string (for base64 images), textContent?: string (for text files), fileObject?: File (original browser File, for future Supabase upload) }`. **(COMPLETED)**
    4.  **Event Handling (Wrapper around Editor/Pills or on Editor for text paste):** **(COMPLETED)**
        *   **Paste Event:** **(COMPLETED)**
            *   Pasted image data (e.g., screenshot): Convert to base64 `dataUrl`, create `LocalAttachment`, add to `pendingAttachments`. **(COMPLETED)**
            *   Pasted plain text *into editor*: Handled by `SimpleMDE`. **(COMPLETED)**
            *   Pasted *files* (from OS): **(COMPLETED)**
                *   Images: Read as base64 `dataUrl`, create `LocalAttachment`. **(COMPLETED)**
                *   Text files (`text/*`): Read content as string, create `LocalAttachment` (store in `textContent`). **(COMPLETED)**
                *   Other types: Initially ignore or show "not supported" pill. **(COMPLETED - basic handling, can be improved)**
        *   **Drop Event (on entire input area):** **(COMPLETED)**
            *   Handle image and text files similarly to file pasting, adding to `pendingAttachments`. **(COMPLETED)**
    5.  **"+" Button Functionality:** **(COMPLETED)**
        *   Trigger `<input type="file" multiple />`. **(COMPLETED)**
        *   Process selected image and text files as per paste/drop logic. **(COMPLETED)**
    6.  **`AttachmentPill.tsx` Component (New):** **(COMPLETED)**
        *   Displays icon, name, image preview (if base64), and a "Remove" (X) button. **(COMPLETED)**
*   **Client-Side `sendMessage` (`app-context.tsx`):** **(COMPLETED)**
    *   Pass `editorContent: string` and `attachmentsData: LocalAttachment[]` (transformed) to the backend. **(COMPLETED)**

### Phase 2: Backend Storage for Initial Attachments & Linking

*   **Objective:** Backend `send_message` RPC stores message text and creates `public.files` entries for attachments, temporarily using `files.url` for base64/text content. **(COMPLETED)**
*   **Backend Tasks (`send_message` RPC):** **(COMPLETED)**
    1.  Accept `p_content TEXT` and `p_attachments_data JSONB[]`. **(COMPLETED)**
    2.  Insert into `public.messages` with `p_content`, get `new_message_id`. **(COMPLETED)**
    3.  For each item in `p_attachments_data`: **(COMPLETED)**
        *   Create a record in `public.files` table. **(COMPLETED)**
        *   Populate `name`, `type`, `size`, `uploaded_by_user_id`, `message_id = new_message_id`, `channel_id`. **(COMPLETED)**
        *   **`url` field (Temporary):** **(COMPLETED)**
            *   If image with `dataUrl`: Store the base64 `dataUrl`. **(COMPLETED)**
            *   If text file with `textContent`: Store the `textContent`. **(COMPLETED)**
        *   Return new message data + array of created `File` records (with IDs and temporary URLs). **(NOTE: RPC currently only returns the message, not the created file records. This is a deviation but client-side optimistic updates handle file display for now.)**
*   **Client-Side Tasks:**
    1.  **`app-context.tsx`:** Update optimistic/final message state with `message.files` from RPC response. **(COMPLETED - optimistic update includes files; logic to preserve optimistic files if RPC doesn't return them is in place.)**
    2.  **`Message.tsx` Rendering:** **(COMPLETED)**
        *   Render `message.content` (Markdown text). **(COMPLETED)**
        *   Separately, iterate `message.files`: **(COMPLETED)**
            *   If `file.url` is base64, render `<img>`. **(COMPLETED)**
            *   If `file.url` is text content, render as downloadable snippet/preview. **(COMPLETED - rendered as download link)**
            *   **NOTE (Bug):** Images/files are not showing up after page reload. This indicates an issue with fetching/populating `message.files` from the database when messages are loaded, despite the data provider being updated to query for them. Needs further investigation.
    3.  **Data Fetching for `ChannelFiles.tsx`:** **(COMPLETED - `getInitialWorkspaceDataForUser` now fetches files directly associated with channels. `ChannelFiles.tsx` itself still needs to be updated to display these.)**
        *   Ensure `getInitialWorkspaceDataForUser` (or similar) fetches associated `public.files` records for a channel and populates `channel.files`. **(COMPLETED)**

### Phase 3: Transition to Full Supabase Storage Integration

*   **Objective:** Replace temporary base64/text content storage with actual file uploads to Supabase Storage for all supported types.
*   **Client-Side Tasks:**
    1.  **`supabase-data-provider.ts`:**
        *   Implement `uploadFileToSupabaseStorage(fileObject: File, onProgressCallback): Promise<FileMetadata>`.
            *   Uploads to Supabase Storage (e.g., `message_attachments` bucket).
            *   Creates entry in `public.files` with `name`, `type`, `size`, `uploaded_by_user_id`, and the actual Supabase Storage `url`. `message_id` and `channel_id` are initially null.
            *   Returns full `File` metadata (matching `src/lib/types.ts File` with DB ID and Storage URL).
    2.  **State Management (`LocalAttachment` type):**
        *   Add `uploadProgress?: number`, `status: 'pending_upload' | 'uploading' | 'complete' | 'error'`, `finalFileId?: string` (ID from `public.files`), `finalUrl?: string`.
    3.  **Update Event Handlers (Paste, Drop, "+ Button"):**
        *   When a file is added:
            1.  Add `LocalAttachment` to `pendingAttachments` with `status: 'pending_upload'`, `fileObject`.
            2.  Asynchronously call `uploadFileToSupabaseStorage`.
            3.  Update `LocalAttachment.uploadProgress` and `status` based on callback. Pill UI reflects this.
            4.  On success, store `finalFileId` and `finalUrl`.
    4.  **"Upload" button in `ChannelFiles.tsx`:**
        *   Wire up to use `uploadFileToSupabaseStorage`. Associates file with `channel_id`. Optionally creates a message.
*   **Backend Tasks (`send_message` RPC):**
    1.  Modify `p_attachments_data` to primarily expect an array of `file_id`s (referencing `public.files` records already created and uploaded via `uploadFileToSupabaseStorage`).
    2.  RPC's main role for attachments becomes updating `message_id` (and `channel_id` if not already set) on these existing `public.files` records.
*   **Client-Side `sendMessage` (`app-context.tsx`):**
    *   Pass array of `finalFileId`s for successfully uploaded attachments.
*   **Rendering (`Message.tsx`):**
    *   `message.files` will contain `File` objects with Supabase Storage URLs.
    *   Image pills render `<img>` from these URLs.
    *   Other file pills provide download links to these URLs.

## 5. Future Considerations

*   Advanced link unfurling for external services (GDrive, Figma, etc.).
*   Support for a wider range of file type previews.
*   User-configurable editor toolbars.
*   Per-file upload error handling and retry mechanisms in the UI.
*   Size limits for initial base64 phase.
