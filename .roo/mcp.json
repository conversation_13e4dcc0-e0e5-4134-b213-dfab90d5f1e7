{"mcpServers": {"github.com/supabase-community/supabase-mcp": {"command": "npx", "args": ["-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"], "disabled": false, "autoApprove": ["list_organizations", "list_projects", "list_edge_functions", "list_tables", "get_project_url", "get_anon_key", "apply_migration", "execute_sql"], "alwaysAllow": ["execute_sql", "list_organizations", "get_organization", "list_projects", "get_project", "get_cost", "confirm_cost", "create_project", "pause_project", "restore_project", "list_tables", "list_extensions", "list_migrations", "apply_migration", "list_edge_functions", "deploy_edge_function", "get_logs", "get_project_url", "get_anon_key", "generate_typescript_types", "create_branch", "list_branches", "delete_branch", "merge_branch", "reset_branch", "rebase_branch"]}}}