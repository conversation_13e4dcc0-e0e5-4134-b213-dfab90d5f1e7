#!/bin/bash

echo "🚀 Starting Error Handling Demo..."
echo ""
echo "This demo showcases the improved error handling for privilege-related errors."
echo ""

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js to run the demo server."
    echo "   You can still view the demo by opening 'error-handling-demo.html' in your browser."
    echo ""
    read -p "Press <PERSON><PERSON> to open the HTML file directly..."
    
    # Try to open the HTML file with the default browser
    if command -v open &> /dev/null; then
        open error-handling-demo.html
    elif command -v xdg-open &> /dev/null; then
        xdg-open error-handling-demo.html
    elif command -v start &> /dev/null; then
        start error-handling-demo.html
    else
        echo "Please manually open 'error-handling-demo.html' in your browser."
    fi
    exit 0
fi

echo "✅ Node.js found. Starting demo server..."
echo ""

# Start the demo server
node server.js
