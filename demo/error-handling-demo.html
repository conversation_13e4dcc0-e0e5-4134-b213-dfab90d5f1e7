<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Error Handling Improvements Demo</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .toast {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #ef4444;
            color: white;
            padding: 12px 16px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            animation: slideIn 0.3s ease-out;
        }
        
        .toast.success {
            background: #10b981;
        }
        
        .toast.warning {
            background: #f59e0b;
        }
        
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 16px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
        
        .error-old {
            background: #fef2f2;
            border: 1px solid #fecaca;
            color: #dc2626;
        }
        
        .error-new {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            color: #0369a1;
        }
    </style>
</head>
<body class="bg-gray-50 min-h-screen">
    <div class="container mx-auto px-4 py-8 max-w-4xl">
        <header class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-2">Error Handling Improvements Demo</h1>
            <p class="text-gray-600">See how privilege-related errors are now handled with user-friendly messages</p>
        </header>

        <div class="grid gap-6">
            <!-- Overview Section -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h2 class="text-xl font-semibold mb-4">🎯 Problem Solved</h2>
                <div class="grid md:grid-cols-2 gap-4">
                    <div class="error-old p-4 rounded-lg">
                        <h3 class="font-semibold mb-2">❌ Before (Raw Database Error)</h3>
                        <p class="text-sm">"Failed to create section: new row violates row-level security policy for table 'sections'"</p>
                    </div>
                    <div class="error-new p-4 rounded-lg">
                        <h3 class="font-semibold mb-2">✅ After (User-Friendly Message)</h3>
                        <p class="text-sm">"Permission denied: You don't have the necessary rights to create sections in this workspace. Please contact your workspace administrator."</p>
                    </div>
                </div>
            </div>

            <!-- Interactive Demo Section -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h2 class="text-xl font-semibold mb-4">🧪 Interactive Demo</h2>
                <p class="text-gray-600 mb-6">Click the buttons below to see how different types of errors are handled:</p>
                
                <div class="grid gap-4" id="demo-scenarios">
                    <!-- Demo scenarios will be populated by JavaScript -->
                </div>
            </div>

            <!-- Technical Implementation -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h2 class="text-xl font-semibold mb-4">⚙️ Technical Implementation</h2>
                <div class="space-y-4">
                    <div>
                        <h3 class="font-semibold mb-2">Before (Manual Error Handling)</h3>
                        <div class="code-block">
if (error) {
  console.error('Error creating section:', error);
  toast.error(`Failed to create section: ${error.message}`);
  return null;
}</div>
                    </div>
                    <div>
                        <h3 class="font-semibold mb-2">After (Centralized Error Handling)</h3>
                        <div class="code-block">
if (error) {
  handleSupabaseError(error, OPERATION_CONTEXTS.CREATE_SECTION, {
    operation: 'section creation',
    fallbackMessage: 'Failed to create section'
  });
  return null;
}</div>
                    </div>
                </div>
            </div>

            <!-- Benefits Section -->
            <div class="bg-white rounded-lg shadow-sm border p-6">
                <h2 class="text-xl font-semibold mb-4">🚀 Key Benefits</h2>
                <div class="grid md:grid-cols-2 gap-4">
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <span class="text-green-500 mr-2">✓</span>
                            <span>User-friendly error messages</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 mr-2">✓</span>
                            <span>Consistent error handling</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 mr-2">✓</span>
                            <span>Actionable guidance for users</span>
                        </li>
                    </ul>
                    <ul class="space-y-2">
                        <li class="flex items-start">
                            <span class="text-green-500 mr-2">✓</span>
                            <span>Centralized error management</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 mr-2">✓</span>
                            <span>Context-aware messaging</span>
                        </li>
                        <li class="flex items-start">
                            <span class="text-green-500 mr-2">✓</span>
                            <span>Maintainable codebase</span>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Error handling utility functions (simplified versions for demo)
        const SUPABASE_ERROR_CODES = {
            RLS_VIOLATION: '42501',
            UNIQUE_VIOLATION: '23505',
            FOREIGN_KEY_VIOLATION: '23503',
            NOT_NULL_VIOLATION: '23502'
        };

        const OPERATION_CONTEXTS = {
            CREATE_SECTION: 'create sections',
            CREATE_CHANNEL: 'create channels',
            UPDATE_SECTION: 'update sections',
            DELETE_SECTION: 'delete sections',
            UPDATE_WORKSPACE_SETTINGS: 'modify workspace settings'
        };

        function isPrivilegeError(error) {
            if (!error) return false;
            
            if (error.code === SUPABASE_ERROR_CODES.RLS_VIOLATION) {
                return true;
            }
            
            const message = error.message?.toLowerCase() || '';
            return (
                message.includes('row-level security policy') ||
                message.includes('permission denied') ||
                message.includes('insufficient privilege') ||
                message.includes('access denied') ||
                message.includes('not authorized') ||
                message.includes('violates row-level security')
            );
        }

        function getPrivilegeErrorMessage(context) {
            return `Permission denied: You don't have the necessary rights to ${context} in this workspace. Please contact your workspace administrator.`;
        }

        function getUserFriendlyErrorMessage(error, context, fallbackMessage) {
            if (isPrivilegeError(error)) {
                return getPrivilegeErrorMessage(context);
            }
            
            switch (error.code) {
                case SUPABASE_ERROR_CODES.UNIQUE_VIOLATION:
                    return 'This item already exists. Please choose a different name.';
                case SUPABASE_ERROR_CODES.FOREIGN_KEY_VIOLATION:
                    return 'This operation cannot be completed due to related data constraints.';
                case SUPABASE_ERROR_CODES.NOT_NULL_VIOLATION:
                    return 'Required information is missing. Please fill in all required fields.';
                default:
                    return fallbackMessage || error.message || 'An unexpected error occurred.';
            }
        }

        function showToast(message, type = 'error') {
            const toast = document.createElement('div');
            toast.className = `toast ${type}`;
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.remove();
            }, 4000);
        }

        // Demo scenarios
        const demoScenarios = [
            {
                name: 'Section Creation - RLS Violation',
                description: 'User tries to create a section without admin rights',
                error: {
                    code: '42501',
                    message: 'new row violates row-level security policy for table "sections"'
                },
                context: OPERATION_CONTEXTS.CREATE_SECTION
            },
            {
                name: 'Channel Creation - Permission Denied',
                description: 'User tries to create a channel without proper permissions',
                error: {
                    code: '42501',
                    message: 'permission denied for table channels'
                },
                context: OPERATION_CONTEXTS.CREATE_CHANNEL
            },
            {
                name: 'Unique Constraint Violation',
                description: 'User tries to create something that already exists',
                error: {
                    code: '23505',
                    message: 'duplicate key value violates unique constraint "sections_name_workspace_id_key"'
                },
                context: OPERATION_CONTEXTS.CREATE_SECTION
            },
            {
                name: 'Generic Database Error',
                description: 'An unexpected database error occurs',
                error: {
                    code: 'UNKNOWN',
                    message: 'connection to server was lost'
                },
                context: OPERATION_CONTEXTS.UPDATE_WORKSPACE_SETTINGS
            }
        ];

        function createDemoScenario(scenario) {
            const div = document.createElement('div');
            div.className = 'border rounded-lg p-4 space-y-3';
            
            div.innerHTML = `
                <div>
                    <h3 class="font-semibold text-sm">${scenario.name}</h3>
                    <p class="text-xs text-gray-600">${scenario.description}</p>
                </div>
                <div class="text-xs text-gray-600 bg-gray-50 p-2 rounded">
                    <strong>Original Error:</strong> ${scenario.error.message}
                </div>
                <div class="flex gap-2">
                    <button class="px-3 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600" onclick="showOldError('${scenario.error.message}')">
                        Show Old Error
                    </button>
                    <button class="px-3 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600" onclick="showNewError(${JSON.stringify(scenario).replace(/"/g, '&quot;')})">
                        Show New Error
                    </button>
                </div>
            `;
            
            return div;
        }

        function showOldError(message) {
            showToast(`Failed to create section: ${message}`, 'error');
        }

        function showNewError(scenarioJson) {
            const scenario = JSON.parse(scenarioJson.replace(/&quot;/g, '"'));
            const userMessage = getUserFriendlyErrorMessage(
                scenario.error, 
                scenario.context,
                'An unexpected error occurred'
            );
            showToast(userMessage, isPrivilegeError(scenario.error) ? 'warning' : 'error');
        }

        // Initialize demo scenarios
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('demo-scenarios');
            demoScenarios.forEach(scenario => {
                container.appendChild(createDemoScenario(scenario));
            });
        });
    </script>
</body>
</html>
