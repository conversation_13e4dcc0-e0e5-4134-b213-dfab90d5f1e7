# Error Handling Demo

This is a standalone demo application that showcases the improved error handling for privilege-related errors in the Thread Flow Social application.

## 🚀 Quick Start

### Option 1: Using Node.js
```bash
cd demo
node server.js
```

### Option 2: Using npm
```bash
cd demo
npm start
```

### Option 3: Direct file access
Simply open `error-handling-demo.html` in your web browser.

## 🌐 Access the Demo

Once the server is running, open your browser and go to:
- **Local:** http://localhost:3001/
- **Demo:** http://localhost:3001/demo

## 📋 Demo Features

### Interactive Error Comparison
- **Before/After Examples:** See raw database errors vs. user-friendly messages
- **Live Toast Notifications:** Experience how errors appear to users
- **Multiple Error Types:** RLS violations, unique constraints, generic errors
- **Context-Aware Messaging:** Different messages based on the operation

### Error Scenarios Included

1. **Section Creation - RLS Violation**
   - Raw: "new row violates row-level security policy for table 'sections'"
   - Improved: "Permission denied: You don't have the necessary rights to create sections..."

2. **Channel Creation - Permission Denied**
   - Raw: "permission denied for table channels"
   - Improved: "Permission denied: You don't have the necessary rights to create channels..."

3. **Unique Constraint Violation**
   - Raw: "duplicate key value violates unique constraint"
   - Improved: "This item already exists. Please choose a different name."

4. **Generic Database Error**
   - Raw: "connection to server was lost"
   - Improved: "An unexpected error occurred during workspace settings update..."

## 🎯 What This Demo Shows

### Problem Solved
- **Technical Jargon Removed:** No more confusing database error messages
- **User-Friendly Language:** Clear, actionable error messages
- **Consistent Experience:** All privilege errors follow the same pattern
- **Actionable Guidance:** Users know what to do (contact admin)

### Technical Implementation
- **Centralized Error Handling:** Single utility for all error processing
- **Context-Aware Messaging:** Different messages based on operation type
- **Error Type Detection:** Automatic detection of privilege vs. other errors
- **Maintainable Code:** Easy to extend and modify

## 🛠️ How It Works

The demo includes simplified versions of the actual error handling utilities:

1. **Error Detection:** Identifies privilege-related errors by code and message patterns
2. **Message Mapping:** Maps technical errors to user-friendly messages
3. **Context Awareness:** Provides different messages based on the operation
4. **Toast Notifications:** Shows how errors appear in the actual application

## 📁 Files Included

- `error-handling-demo.html` - Main demo page with interactive examples
- `server.js` - Simple Node.js server to serve the demo
- `package.json` - Demo package configuration
- `README.md` - This documentation

## 🔧 Customization

You can modify the demo by:
- Adding new error scenarios in the `demoScenarios` array
- Changing the styling in the `<style>` section
- Updating error messages in the utility functions

## 📱 Mobile Friendly

The demo is responsive and works well on mobile devices, tablets, and desktops.

## 🎨 Features

- **Interactive Buttons:** Click to see old vs. new error messages
- **Visual Comparison:** Side-by-side before/after examples
- **Live Notifications:** Toast messages show actual user experience
- **Code Examples:** See the technical implementation
- **Benefits Overview:** Understand the improvements made

## 🚪 Stopping the Demo

To stop the demo server, press `Ctrl+C` in the terminal where it's running.
