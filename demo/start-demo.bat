@echo off
echo 🚀 Starting Error Handling Demo...
echo.
echo This demo showcases the improved error handling for privilege-related errors.
echo.

REM Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js to run the demo server.
    echo    You can still view the demo by opening 'error-handling-demo.html' in your browser.
    echo.
    pause
    start error-handling-demo.html
    exit /b 0
)

echo ✅ Node.js found. Starting demo server...
echo.

REM Start the demo server
node server.js
