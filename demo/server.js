const http = require('http');
const fs = require('fs');
const path = require('path');

const PORT = 3001;

const server = http.createServer((req, res) => {
    // Handle root path
    if (req.url === '/' || req.url === '/demo') {
        const filePath = path.join(__dirname, 'error-handling-demo.html');
        
        fs.readFile(filePath, 'utf8', (err, data) => {
            if (err) {
                res.writeHead(500, { 'Content-Type': 'text/plain' });
                res.end('Error loading demo page');
                return;
            }
            
            res.writeHead(200, { 'Content-Type': 'text/html' });
            res.end(data);
        });
        return;
    }
    
    // Handle 404
    res.writeHead(404, { 'Content-Type': 'text/plain' });
    res.end('Page not found');
});

server.listen(PORT, () => {
    console.log(`🚀 Error Handling Demo Server running at:`);
    console.log(`   Local:   http://localhost:${PORT}/`);
    console.log(`   Demo:    http://localhost:${PORT}/demo`);
    console.log('');
    console.log('📋 Demo Features:');
    console.log('   • Interactive error message comparison');
    console.log('   • Before/after examples');
    console.log('   • Live toast notifications');
    console.log('   • Technical implementation details');
    console.log('');
    console.log('Press Ctrl+C to stop the server');
});

// Handle graceful shutdown
process.on('SIGINT', () => {
    console.log('\n👋 Shutting down demo server...');
    server.close(() => {
        console.log('Demo server stopped');
        process.exit(0);
    });
});
